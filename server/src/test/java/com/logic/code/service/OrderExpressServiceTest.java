package com.logic.code.service;

import com.logic.code.common.utils.ExcelUtils;
import com.logic.code.entity.order.OrderExpress;
import org.apache.poi.ss.usermodel.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OrderExpress服务测试类
 * <AUTHOR>
 * @date 2025/7/11
 */
@SpringBootTest
public class OrderExpressServiceTest {

    // 注释掉Spring相关注解，专注于Excel功能测试
     @Autowired
     private OrderExpressService orderExpressService;

    /**
     * 测试通过Excel导入OrderExpress数据（不涉及数据库操作）
     */
    @Test
    public void testImportOrderExpressFromExcel() {
        try {
            // 1. 创建测试Excel文件
            String testFilePath = createTestExcelFile();

            // 2. 读取Excel文件数据
            List<OrderExpress> orderExpressList = readOrderExpressFromExcel(testFilePath);

            System.out.println("Excel导入测试成功！成功解析了 " + orderExpressList.size() + " 条OrderExpress记录");

            // 7. 模拟数据库插入操作（实际项目中会调用service层方法）
            System.out.println("模拟数据库插入操作：");
            for (int i = 0; i < orderExpressList.size(); i++) {
                OrderExpress express = orderExpressList.get(i);
                // 设置创建时间和更新时间
                express.setCreateTime(new Date());
                express.setUpdateTime(new Date());
                express.setRequestTime(new Date());

                System.out.println("第" + (i + 1) + "条记录：订单ID=" + express.getOrderId() +
                                 ", 物流公司=" + express.getShipperName() +
                                 ", 快递单号=" + express.getLogisticCode());

                orderExpressService.insert(express);
            }

        } catch (Exception e) {
            fail("Excel导入测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建测试用的Excel文件
     */
    private String createTestExcelFile() throws IOException {
        String fileName = "test_order_express_import.xlsx";
        String filePath = System.getProperty("java.io.tmpdir") + "/" + fileName;

        // 创建工作簿
        Workbook workbook = ExcelUtils.createWorkbook();
        Sheet sheet = workbook.createSheet("OrderExpress");

        // 创建样式
        CellStyle headerStyle = ExcelUtils.createHeaderStyle(workbook);
        CellStyle dataStyle = ExcelUtils.createDataStyle(workbook);

        // 创建表头
        String[] headers = {
            "订单ID", "物流公司ID", "物流公司名称", "物流公司代码", "快递单号"
        };

        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            ExcelUtils.setCellValue(cell, headers[i], headerStyle);
        }

        // 创建测试数据
        Object[][] testData = {
                {216, 8, "邮政EMS", "EMS", "8167764288608"},
                {218, 8, "邮政EMS", "EMS", "8167754699508"},
             /*   {203, 8, "邮政EMS", "EMS", "8163537272908"},
                {204, 8, "邮政EMS", "EMS", "8164147633408"},
                {205, 8, "邮政EMS", "EMS", "8164606454208"},
                {200, 8, "顺丰速运", "SF", "SF3191064401818"},
                {201, 8, "顺丰速运", "SF", "SF3191654485829"},*/

        };

        // 填充数据行
        for (int i = 0; i < testData.length; i++) {
            Row dataRow = sheet.createRow(i + 1);
            Object[] rowData = testData[i];

            for (int j = 0; j < rowData.length; j++) {
                Cell cell = dataRow.createCell(j);
                ExcelUtils.setCellValue(cell, rowData[j], dataStyle);
            }
        }

        // 自动调整列宽
        ExcelUtils.autoSizeColumns(sheet, headers.length);

        // 保存文件
        ExcelUtils.saveWorkbook(workbook, filePath);
        workbook.close();

        System.out.println("测试Excel文件已创建: " + filePath);
        return filePath;
    }

    /**
     * 从Excel文件读取OrderExpress数据
     */
    private List<OrderExpress> readOrderExpressFromExcel(String filePath) throws IOException {
        List<OrderExpress> orderExpressList = new ArrayList<>();

        try (Workbook workbook = ExcelUtils.readWorkbook(filePath)) {
            Sheet sheet = workbook.getSheetAt(0);

            // 跳过表头行，从第二行开始读取数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                OrderExpress orderExpress = new OrderExpress();

                // 读取各列数据
                orderExpress.setOrderId(ExcelUtils.getCellValueAsInteger(row.getCell(0)));
                orderExpress.setShipperId(ExcelUtils.getCellValueAsInteger(row.getCell(1)));
                orderExpress.setShipperName(ExcelUtils.getCellValueAsString(row.getCell(2)));
                orderExpress.setShipperCode(ExcelUtils.getCellValueAsString(row.getCell(3)));
                orderExpress.setLogisticCode(ExcelUtils.getCellValueAsString(row.getCell(4)));

                // 验证必填字段
                if (orderExpress.getOrderId() != null && orderExpress.getLogisticCode() != null
                    && !orderExpress.getLogisticCode().trim().isEmpty()) {
                    orderExpressList.add(orderExpress);
                }
            }
        }

        return orderExpressList;
    }

    /**
     * 测试Excel文件读取功能
     */
    @Test
    public void testExcelReadFunctionality() {
        try {
            // 创建测试文件
            String testFilePath = createTestExcelFile();

            // 测试基本读取功能
            List<List<String>> data = ExcelUtils.readExcelData(testFilePath);
            assertNotNull(data);
            assertFalse(data.isEmpty());
            assertEquals(4, data.size()); // 1个表头行 + 3个数据行

            // 验证表头
            List<String> headerRow = data.get(0);
            assertEquals("订单ID", headerRow.get(0));
            assertEquals("物流公司名称", headerRow.get(2));
            assertEquals("快递单号", headerRow.get(4));

            // 验证数据行
            List<String> firstDataRow = data.get(1);
            assertEquals("1001", firstDataRow.get(0));
            assertEquals("顺丰速运", firstDataRow.get(2));
            assertEquals("SF1234567890", firstDataRow.get(4));

            System.out.println("Excel读取功能测试成功！");

        } catch (Exception e) {
            fail("Excel读取功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试Excel导入数据验证功能
     */
    @Test
    public void testExcelImportDataValidation() {
        try {
            // 创建包含无效数据的Excel文件
            String testFilePath = createInvalidDataExcelFile();

            // 读取Excel文件数据
            List<OrderExpress> orderExpressList = readOrderExpressFromExcel(testFilePath);

            // 验证只有有效数据被读取（无效数据被过滤掉）
            assertNotNull(orderExpressList);
            assertEquals(2, orderExpressList.size()); // 只有2条有效数据

            // 验证有效数据
            OrderExpress firstExpress = orderExpressList.get(0);
            assertEquals(Integer.valueOf(2001), firstExpress.getOrderId());
            assertEquals("有效快递单号1", firstExpress.getLogisticCode());

            OrderExpress secondExpress = orderExpressList.get(1);
            assertEquals(Integer.valueOf(2003), secondExpress.getOrderId());
            assertEquals("有效快递单号2", secondExpress.getLogisticCode());

            System.out.println("Excel数据验证测试成功！过滤了无效数据，保留了 " + orderExpressList.size() + " 条有效记录");

        } catch (Exception e) {
            fail("Excel数据验证测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建包含无效数据的测试Excel文件
     */
    private String createInvalidDataExcelFile() throws IOException {
        String fileName = "test_invalid_order_express_import.xlsx";
        String filePath = System.getProperty("java.io.tmpdir") + "/" + fileName;

        // 创建工作簿
        Workbook workbook = ExcelUtils.createWorkbook();
        Sheet sheet = workbook.createSheet("OrderExpress");

        // 创建样式
        CellStyle headerStyle = ExcelUtils.createHeaderStyle(workbook);
        CellStyle dataStyle = ExcelUtils.createDataStyle(workbook);

        // 创建表头
        String[] headers = {
            "订单ID", "物流公司ID", "物流公司名称", "物流公司代码", "快递单号"
        };

        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            ExcelUtils.setCellValue(cell, headers[i], headerStyle);
        }

        // 创建测试数据（包含无效数据）
        Object[][] testData = {
            {2001, 1, "顺丰速运", "SF", "有效快递单号1",}, // 有效数据
            {2003, 3, "中通快递", "ZTO", "有效快递单号2"}, // 有效数据
        };

        // 填充数据行
        for (int i = 0; i < testData.length; i++) {
            Row dataRow = sheet.createRow(i + 1);
            Object[] rowData = testData[i];

            for (int j = 0; j < rowData.length; j++) {
                Cell cell = dataRow.createCell(j);
                ExcelUtils.setCellValue(cell, rowData[j], dataStyle);
            }
        }

        // 自动调整列宽
        ExcelUtils.autoSizeColumns(sheet, headers.length);

        // 保存文件
        ExcelUtils.saveWorkbook(workbook, filePath);
        workbook.close();

        System.out.println("包含无效数据的测试Excel文件已创建: " + filePath);
        return filePath;
    }

    /**
     * 批量导入OrderExpress数据的工具方法
     * 在实际项目中，这个方法可以放在Service层
     */
    public static List<OrderExpress> importOrderExpressFromExcelFile(String filePath) throws IOException {
        List<OrderExpress> orderExpressList = new ArrayList<>();

        try (Workbook workbook = ExcelUtils.readWorkbook(filePath)) {
            Sheet sheet = workbook.getSheetAt(0);

            // 跳过表头行，从第二行开始读取数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                OrderExpress orderExpress = new OrderExpress();

                // 读取各列数据
                orderExpress.setOrderId(ExcelUtils.getCellValueAsInteger(row.getCell(0)));
                orderExpress.setShipperId(ExcelUtils.getCellValueAsInteger(row.getCell(1)));
                orderExpress.setShipperName(ExcelUtils.getCellValueAsString(row.getCell(2)));
                orderExpress.setShipperCode(ExcelUtils.getCellValueAsString(row.getCell(3)));
                orderExpress.setLogisticCode(ExcelUtils.getCellValueAsString(row.getCell(4)));
                orderExpress.setTraces(ExcelUtils.getCellValueAsString(row.getCell(5)));
                orderExpress.setIsFinish(ExcelUtils.getCellValueAsBoolean(row.getCell(6)));
                orderExpress.setRequestCount(ExcelUtils.getCellValueAsInteger(row.getCell(7)));
                orderExpress.setLogisticsStatus(ExcelUtils.getCellValueAsString(row.getCell(8)));

                // 验证必填字段
                if (orderExpress.getOrderId() != null && orderExpress.getLogisticCode() != null
                    && !orderExpress.getLogisticCode().trim().isEmpty()) {

                    // 设置默认值
                    if (orderExpress.getRequestCount() == null) {
                        orderExpress.setRequestCount(0);
                    }
                    if (orderExpress.getIsFinish() == null) {
                        orderExpress.setIsFinish(false);
                    }

                    orderExpressList.add(orderExpress);
                }
            }
        }

        return orderExpressList;
    }
}
