package com.logic.code.service;

import com.logic.code.mapper.PromotionEarningsMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 聚合查询修复验证测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class AggregateQueryFixTest {

    @Autowired
    private PromotionEarningsService promotionEarningsService;

    @Autowired
    private PromotionEarningsMapper promotionEarningsMapper;


    /**
     * 测试Service方法是否返回正确结果
     */
    @Test
    public void testServiceMethodsReturnValues() {
        Integer userId = 1;

        // 测试收益统计
        Map<String, Object> earningsStats = promotionEarningsService.getEarningsStats(userId);
        assertNotNull(earningsStats, "收益统计不应为null");

        // 验证各个字段都不为null
        assertNotNull(earningsStats.get("totalEarnings"), "总收益不应为null");
        assertNotNull(earningsStats.get("todayEarnings"), "今日收益不应为null");
        assertNotNull(earningsStats.get("lastMonthEarnings"), "上月收益不应为null");
        assertNotNull(earningsStats.get("pendingEarnings"), "待确认收益不应为null");
        assertNotNull(earningsStats.get("todayEstimated"), "今日预估收入不应为null");
        assertNotNull(earningsStats.get("monthEstimated"), "本月预估收入不应为null");

        System.out.println("✅ Service方法返回值正常");
        System.out.println("总收益: " + earningsStats.get("totalEarnings"));
        System.out.println("今日收益: " + earningsStats.get("todayEarnings"));
        System.out.println("上月收益: " + earningsStats.get("lastMonthEarnings"));
        System.out.println("待确认收益: " + earningsStats.get("pendingEarnings"));
    }

    /**
     * 测试月度订单明细是否正常
     */
    @Test
    public void testMonthOrderDetailFixed() {
        Integer userId = 1;
        String month = "2024-12";

        Map<String, Object> result = promotionEarningsService.getMonthOrderDetail(userId, month);
        assertNotNull(result, "月度订单明细不应为null");

        assertTrue(result.containsKey("orderList"), "应包含订单列表");
        assertTrue(result.containsKey("monthStats"), "应包含月度统计");

        Map<String, Object> monthStats = (Map<String, Object>) result.get("monthStats");
        assertNotNull(monthStats, "月度统计不应为null");

        // 验证统计数据不为null
        assertNotNull(monthStats.get("estimatedIncome"), "预估收入不应为null");
        assertNotNull(monthStats.get("orderCount"), "订单数量不应为null");
        assertNotNull(monthStats.get("commissionAmount"), "佣金金额不应为null");

        System.out.println("✅ 月度订单明细查询正常");
        System.out.println("预估收入: " + monthStats.get("estimatedIncome"));
        System.out.println("订单数量: " + monthStats.get("orderCount"));
        System.out.println("佣金金额: " + monthStats.get("commissionAmount"));
    }

    /**
     * 测试月度收益数据是否正常
     */
    @Test
    public void testMonthEarningsDataFixed() {
        Integer userId = 1;

        Map<String, Object> monthlyStats = promotionEarningsService.getMonthlyEarningsStats(userId);
        assertNotNull(monthlyStats, "月度收益统计不应为null");

        assertTrue(monthlyStats.containsKey("monthlyList"), "应包含月度列表");
        assertTrue(monthlyStats.containsKey("totalStats"), "应包含总计统计");

        System.out.println("✅ 月度收益数据查询正常");
        System.out.println("月度统计: " + monthlyStats);
    }

    /**
     * 测试数据一致性
     */
    @Test
    public void testDataConsistency() {
        Integer userId = 1;

        // 获取总收益
        Map<String, Object> earningsStats = promotionEarningsService.getEarningsStats(userId);
        String totalEarningsStr = (String) earningsStats.get("totalEarnings");
        BigDecimal totalEarnings = new BigDecimal(totalEarningsStr);

        // 获取直接查询的总收益
        BigDecimal directTotalEarnings = promotionEarningsMapper.selectTotalEarnings(userId);

        // 验证两种方式获取的总收益应该一致
        assertEquals(0, totalEarnings.compareTo(directTotalEarnings),
                "Service方法和Mapper方法获取的总收益应该一致");

        System.out.println("✅ 数据一致性验证通过");
        System.out.println("Service总收益: " + totalEarnings);
        System.out.println("Mapper总收益: " + directTotalEarnings);
    }

    /**
     * 测试边界情况
     */
    @Test
    public void testEdgeCasesFixed() {
        // 测试不存在的用户
        Integer nonExistentUserId = 99999;

        BigDecimal totalEarnings = promotionEarningsMapper.selectTotalEarnings(nonExistentUserId);
        assertNotNull(totalEarnings, "不存在用户的总收益不应为null");
        assertEquals(0, totalEarnings.compareTo(BigDecimal.ZERO), "不存在用户的总收益应为0");

        Map<String, Object> result = promotionEarningsService.getMonthOrderDetail(nonExistentUserId, "2024-12");
        assertNotNull(result, "不存在用户的月度明细不应为null");

        System.out.println("✅ 边界情况处理正常");
    }

    /**
     * 性能对比测试
     */
    @Test
    public void testPerformanceComparison() {
        Integer userId = 1;
        int iterations = 10;

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < iterations; i++) {
            promotionEarningsService.getEarningsStats(userId);
            promotionEarningsService.getMonthOrderDetail(userId, "2024-12");
        }

        long endTime = System.currentTimeMillis();
        long avgTime = (endTime - startTime) / iterations;

        System.out.println("✅ 性能测试完成");
        System.out.println("平均响应时间: " + avgTime + "ms");

        // 验证性能在合理范围内
        assertTrue(avgTime < 2000, "平均响应时间应小于2秒，当前: " + avgTime + "ms");
    }
}
