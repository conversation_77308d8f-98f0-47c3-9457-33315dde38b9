package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.entity.goods.Goods;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.User;
import com.logic.code.mapper.GoodsMapper;
import com.logic.code.mapper.OrderMapper;
import com.logic.code.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 统计服务类
 * <AUTHOR>
 * @date 2025/1/5
 */
@Service
public class StatisticService {

    @Autowired
    private GoodsMapper goodsMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private UserMapper userMapper;

    /**
     * 获取商品统计基础数据
     * @param dateRange 时间范围，格式：yyyy/MM/dd-yyyy/MM/dd
     * @return 商品统计基础数据
     */
    public Map<String, Object> getProductBasicStatistics(String dateRange) {
        Map<String, Object> result = new HashMap<>();

        // 解析时间范围
        LocalDateTime[] dateRangeArray = parseDateRange(dateRange);
        LocalDateTime startDate = dateRangeArray[0];
        LocalDateTime endDate = dateRangeArray[1];

        // 获取上一个周期的时间范围（用于计算环比）
        long daysBetween = java.time.Duration.between(startDate, endDate).toDays();
        LocalDateTime prevStartDate = startDate.minusDays(daysBetween + 1);
        LocalDateTime prevEndDate = startDate.minusDays(1);

        // 商品浏览量（模拟数据，实际应该从访问日志表获取）
        Map<String, Object> browse = new HashMap<>();
        browse.put("num", generateRandomStatistic(1000, 5000));
        browse.put("percent", generateRandomPercent());
        result.put("browse", browse);

        // 商品访客数（模拟数据）
        Map<String, Object> user = new HashMap<>();
        user.put("num", generateRandomStatistic(500, 2000));
        user.put("percent", generateRandomPercent());
        result.put("user", user);

        // 支付件数（从订单表统计）
        Map<String, Object> pay = getPayStatistics(startDate, endDate, prevStartDate, prevEndDate);
        result.put("pay", pay);

        // 支付金额（从订单表统计）
        Map<String, Object> payPrice = getPayPriceStatistics(startDate, endDate, prevStartDate, prevEndDate);
        result.put("payPrice", payPrice);

        // 退款件数（模拟数据，实际应该从退款表获取）
        Map<String, Object> refund = new HashMap<>();
        refund.put("num", generateRandomStatistic(10, 100));
        refund.put("percent", generateRandomPercent());
        result.put("refund", refund);

        // 退款金额（模拟数据）
        Map<String, Object> refundPrice = new HashMap<>();
        refundPrice.put("num", generateRandomStatistic(1000, 10000));
        refundPrice.put("percent", generateRandomPercent());
        result.put("refundPrice", refundPrice);

        return result;
    }

    /**
     * 获取商品统计趋势数据
     * @param dateRange 时间范围
     * @return 商品统计趋势数据
     */
    public Map<String, Object> getProductTrendStatistics(String dateRange) {
        Map<String, Object> result = new HashMap<>();

        // 解析时间范围
        LocalDateTime[] dateRangeArray = parseDateRange(dateRange);
        LocalDateTime startDate = dateRangeArray[0];
        LocalDateTime endDate = dateRangeArray[1];

        // 生成日期轴数据
        List<String> xAxis = generateDateAxis(startDate, endDate);
        result.put("xAxis", xAxis);

        // 生成系列数据
        List<Map<String, Object>> series = new ArrayList<>();

        // 商品浏览量趋势
        Map<String, Object> browseSeries = new HashMap<>();
        browseSeries.put("name", "商品浏览量");
        browseSeries.put("type", "line");
        browseSeries.put("yAxisIndex", 1);
        browseSeries.put("data", generateTrendData(xAxis.size(), 100, 500));
        series.add(browseSeries);

        // 商品访客数趋势
        Map<String, Object> userSeries = new HashMap<>();
        userSeries.put("name", "商品访客数");
        userSeries.put("type", "line");
        userSeries.put("yAxisIndex", 1);
        userSeries.put("data", generateTrendData(xAxis.size(), 50, 200));
        series.add(userSeries);

        // 支付金额趋势
        Map<String, Object> payPriceSeries = new HashMap<>();
        payPriceSeries.put("name", "支付金额");
        payPriceSeries.put("type", "line");
        payPriceSeries.put("yAxisIndex", 0);
        payPriceSeries.put("data", generateTrendData(xAxis.size(), 1000, 5000));
        series.add(payPriceSeries);

        // 退款金额趋势
        Map<String, Object> refundPriceSeries = new HashMap<>();
        refundPriceSeries.put("name", "退款金额");
        refundPriceSeries.put("type", "line");
        refundPriceSeries.put("yAxisIndex", 0);
        refundPriceSeries.put("data", generateTrendData(xAxis.size(), 100, 1000));
        series.add(refundPriceSeries);

        result.put("series", series);

        return result;
    }

    /**
     * 获取商品排行统计数据
     * @param dateRange 时间范围
     * @param page 页码
     * @param limit 每页数量
     * @return 商品排行统计数据
     */
    public Map<String, Object> getProductRankingStatistics(String dateRange, Integer page, Integer limit) {
        Map<String, Object> result = new HashMap<>();

        // 查询商品列表（按销量排序）
        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", false);
        queryWrapper.orderByDesc("sell_volume");
        queryWrapper.last("LIMIT " + ((page - 1) * limit) + ", " + limit);

        List<Goods> goodsList = goodsMapper.selectList(queryWrapper);

        // 转换为前端需要的格式
        List<Map<String, Object>> list = new ArrayList<>();
        for (int i = 0; i < goodsList.size(); i++) {
            Goods goods = goodsList.get(i);
            Map<String, Object> item = new HashMap<>();
            item.put("id", goods.getId());
            item.put("store_name", goods.getName()); // 使用商品名称作为店铺名称
            item.put("image", goods.getPrimaryPicUrl()); // 使用主图URL
            item.put("sales", goods.getSellVolume() != null ? goods.getSellVolume() : 0);
            item.put("price", goods.getRetailPrice() != null ? goods.getRetailPrice() : BigDecimal.ZERO);
            item.put("browse", generateRandomStatistic(100, 1000)); // 浏览量（模拟数据）
            item.put("cart", generateRandomStatistic(10, 100)); // 加购数（模拟数据）
            item.put("pay_count", generateRandomStatistic(5, 50)); // 支付件数（模拟数据）
            item.put("pay_price", generateRandomStatistic(500, 5000)); // 支付金额（模拟数据）
            list.add(item);
        }

        // 获取总数
        QueryWrapper<Goods> countWrapper = new QueryWrapper<>();
        countWrapper.eq("is_delete", false);
        Long total = goodsMapper.selectCount(countWrapper);

        result.put("list", list);
        result.put("count", total);

        return result;
    }

    /**
     * 导出商品统计数据
     * @param dateRange 时间范围
     * @return 导出文件URL
     */
    public Map<String, Object> exportProductStatistics(String dateRange) {
        Map<String, Object> result = new HashMap<>();

        // 模拟导出文件URL
        List<String> urls = new ArrayList<>();
        urls.add("/exports/product_statistics_" + System.currentTimeMillis() + ".xlsx");

        result.put("url", urls);

        return result;
    }

    /**
     * 获取用户统计基础数据
     * @param dateRange 时间范围
     * @param channelType 渠道类型
     * @return 用户统计基础数据
     */
    public Map<String, Object> getUserBasicStatistics(String dateRange, String channelType) {
        Map<String, Object> result = new HashMap<>();

        // 解析时间范围
        LocalDateTime[] dateRangeArray = parseDateRange(dateRange);
        LocalDateTime startDate = dateRangeArray[0];
        LocalDateTime endDate = dateRangeArray[1];

        // 累计用户
        Map<String, Object> cumulativeUser = new HashMap<>();
        QueryWrapper<User> userWrapper = new QueryWrapper<>();
        // User表没有is_delete字段，移除该条件
        Long totalUsers = userMapper.selectCount(userWrapper);
        cumulativeUser.put("num", totalUsers);
        cumulativeUser.put("percent", generateRandomPercent());
        result.put("cumulativeUser", cumulativeUser);

        // 访客数（模拟数据）
        Map<String, Object> people = new HashMap<>();
        people.put("num", generateRandomStatistic(500, 2000));
        people.put("percent", generateRandomPercent());
        result.put("people", people);

        // 浏览量（模拟数据）
        Map<String, Object> browse = new HashMap<>();
        browse.put("num", generateRandomStatistic(1000, 5000));
        browse.put("percent", generateRandomPercent());
        result.put("browse", browse);

        // 新增用户数
        Map<String, Object> newUser = getNewUserStatistics(startDate, endDate);
        result.put("newUser", newUser);

        // 成交用户数（模拟数据）
        Map<String, Object> payPeople = new HashMap<>();
        payPeople.put("num", generateRandomStatistic(100, 500));
        payPeople.put("percent", generateRandomPercent());
        result.put("payPeople", payPeople);

        // 付费会员数（模拟数据）
        Map<String, Object> payUser = new HashMap<>();
        payUser.put("num", generateRandomStatistic(50, 200));
        payUser.put("percent", generateRandomPercent());
        result.put("payUser", payUser);

        return result;
    }

    /**
     * 获取用户统计趋势数据
     * @param dateRange 时间范围
     * @param channelType 渠道类型
     * @return 用户统计趋势数据
     */
    public Map<String, Object> getUserTrendStatistics(String dateRange, String channelType) {
        Map<String, Object> result = new HashMap<>();

        // 解析时间范围
        LocalDateTime[] dateRangeArray = parseDateRange(dateRange);
        LocalDateTime startDate = dateRangeArray[0];
        LocalDateTime endDate = dateRangeArray[1];

        // 生成日期轴数据
        List<String> xAxis = generateDateAxis(startDate, endDate);
        result.put("xAxis", xAxis);

        // 生成系列数据
        List<Map<String, Object>> series = new ArrayList<>();

        // 新增用户趋势
        Map<String, Object> newUserSeries = new HashMap<>();
        newUserSeries.put("name", "新增用户");
        newUserSeries.put("type", "line");
        newUserSeries.put("data", generateTrendData(xAxis.size(), 10, 50));
        series.add(newUserSeries);

        // 访客数趋势
        Map<String, Object> peopleSeries = new HashMap<>();
        peopleSeries.put("name", "访客数");
        peopleSeries.put("type", "line");
        peopleSeries.put("data", generateTrendData(xAxis.size(), 50, 200));
        series.add(peopleSeries);

        result.put("series", series);

        return result;
    }

    /**
     * 获取微信用户统计数据
     * @param dateRange 时间范围
     * @param channelType 渠道类型
     * @return 微信用户统计数据
     */
    public Map<String, Object> getWechatStatistics(String dateRange, String channelType) {
        Map<String, Object> result = new HashMap<>();

        // 新增关注用户数（模拟数据）
        Map<String, Object> subscribe = new HashMap<>();
        subscribe.put("num", generateRandomStatistic(20, 100));
        subscribe.put("percent", generateRandomPercent());
        result.put("subscribe", subscribe);

        // 新增取关用户数（模拟数据）
        Map<String, Object> unSubscribe = new HashMap<>();
        unSubscribe.put("num", generateRandomStatistic(5, 30));
        unSubscribe.put("percent", generateRandomPercent());
        result.put("unSubscribe", unSubscribe);

        // 净增用户数（模拟数据）
        Map<String, Object> increaseSubscribe = new HashMap<>();
        increaseSubscribe.put("num", generateRandomStatistic(10, 80));
        increaseSubscribe.put("percent", generateRandomPercent());
        result.put("increaseSubscribe", increaseSubscribe);

        // 累积关注用户数（模拟数据）
        Map<String, Object> cumulativeSubscribe = new HashMap<>();
        cumulativeSubscribe.put("num", generateRandomStatistic(1000, 5000));
        cumulativeSubscribe.put("percent", generateRandomPercent());
        result.put("cumulativeSubscribe", cumulativeSubscribe);

        // 累积取关用户数（模拟数据）
        Map<String, Object> cumulativeUnSubscribe = new HashMap<>();
        cumulativeUnSubscribe.put("num", generateRandomStatistic(100, 500));
        cumulativeUnSubscribe.put("percent", generateRandomPercent());
        result.put("cumulativeUnSubscribe", cumulativeUnSubscribe);

        return result;
    }

    /**
     * 获取微信用户趋势数据
     * @param dateRange 时间范围
     * @param channelType 渠道类型
     * @return 微信用户趋势数据
     */
    public Map<String, Object> getWechatTrendStatistics(String dateRange, String channelType) {
        Map<String, Object> result = new HashMap<>();

        // 解析时间范围
        LocalDateTime[] dateRangeArray = parseDateRange(dateRange);
        LocalDateTime startDate = dateRangeArray[0];
        LocalDateTime endDate = dateRangeArray[1];

        // 生成日期轴数据
        List<String> xAxis = generateDateAxis(startDate, endDate);
        result.put("xAxis", xAxis);

        // 生成系列数据
        List<Map<String, Object>> series = new ArrayList<>();

        // 新增关注用户趋势
        Map<String, Object> subscribeSeries = new HashMap<>();
        subscribeSeries.put("name", "新增关注用户");
        subscribeSeries.put("type", "line");
        subscribeSeries.put("data", generateTrendData(xAxis.size(), 5, 30));
        series.add(subscribeSeries);

        // 新增取关用户趋势
        Map<String, Object> unSubscribeSeries = new HashMap<>();
        unSubscribeSeries.put("name", "新增取关用户");
        unSubscribeSeries.put("type", "line");
        unSubscribeSeries.put("data", generateTrendData(xAxis.size(), 1, 10));
        series.add(unSubscribeSeries);

        result.put("series", series);

        return result;
    }

    /**
     * 获取用户地域分布数据
     * @param dateRange 时间范围
     * @param channelType 渠道类型
     * @return 用户地域分布数据
     */
    public Map<String, Object> getUserRegionStatistics(String dateRange, String channelType) {
        Map<String, Object> result = new HashMap<>();

        // 模拟地域分布数据
        List<Map<String, Object>> regionData = new ArrayList<>();
        String[] provinces = {"北京", "上海", "广东", "浙江", "江苏", "山东", "河南", "四川", "湖北", "湖南"};

        for (String province : provinces) {
            Map<String, Object> item = new HashMap<>();
            item.put("name", province);
            item.put("value", generateRandomStatistic(100, 1000));
            regionData.add(item);
        }

        result.put("data", regionData);

        return result;
    }

    /**
     * 获取用户性别分布数据
     * @param dateRange 时间范围
     * @param channelType 渠道类型
     * @return 用户性别分布数据
     */
    public Map<String, Object> getUserSexStatistics(String dateRange, String channelType) {
        Map<String, Object> result = new HashMap<>();

        // 模拟性别分布数据
        List<Map<String, Object>> sexData = new ArrayList<>();

        Map<String, Object> male = new HashMap<>();
        male.put("name", "男");
        male.put("value", generateRandomStatistic(400, 800));
        sexData.add(male);

        Map<String, Object> female = new HashMap<>();
        female.put("name", "女");
        female.put("value", generateRandomStatistic(600, 1200));
        sexData.add(female);

        Map<String, Object> unknown = new HashMap<>();
        unknown.put("name", "未知");
        unknown.put("value", generateRandomStatistic(50, 200));
        sexData.add(unknown);

        result.put("data", sexData);

        return result;
    }

    /**
     * 导出用户统计数据
     * @param dateRange 时间范围
     * @param channelType 渠道类型
     * @return 导出文件URL
     */
    public Map<String, Object> exportUserStatistics(String dateRange, String channelType) {
        Map<String, Object> result = new HashMap<>();

        // 模拟导出文件URL
        List<String> urls = new ArrayList<>();
        urls.add("/exports/user_statistics_" + System.currentTimeMillis() + ".xlsx");

        result.put("url", urls);

        return result;
    }

    /**
     * 获取交易统计数据
     * @param dateRange 时间范围
     * @return 交易统计数据
     */
    public Map<String, Object> getTopTradeStatistics(String dateRange) {
        Map<String, Object> result = new HashMap<>();

        // 今日数据（模拟）
        Map<String, Object> today = new HashMap<>();
        today.put("sales_today", generateRandomStatistic(1000, 5000));
        today.put("order_today", generateRandomStatistic(50, 200));
        today.put("user_today", generateRandomStatistic(30, 100));
        today.put("browse_today", generateRandomStatistic(500, 2000));

        result.put("today", today);

        return result;
    }

    /**
     * 获取交易概况数据
     * @param dateRange 时间范围
     * @return 交易概况数据
     */
    public Map<String, Object> getBottomTradeStatistics(String dateRange) {
        Map<String, Object> result = new HashMap<>();

        // 交易概况系列数据
        List<Map<String, Object>> series = new ArrayList<>();

        String[] tradeTypes = {
            "营业额", "交易金额", "商品支付金额", "购买会员金额",
            "充值金额", "线下收银金额", "支出金额", "余额支付金额",
            "支付佣金金额", "商品退款金额"
        };

        for (String type : tradeTypes) {
            Map<String, Object> item = new HashMap<>();
            item.put("name", type);
            item.put("value", generateRandomStatistic(1000, 10000));
            item.put("percent", generateRandomPercent());
            series.add(item);
        }

        result.put("series", series);
        result.put("export", "/exports/trade_statistics_" + System.currentTimeMillis() + ".xlsx");

        return result;
    }

    /**
     * 获取订单统计基础数据
     * @param dateRange 时间范围
     * @return 订单统计基础数据
     */
    public Map<String, Object> getOrderBasicStatistics(String dateRange) {
        Map<String, Object> result = new HashMap<>();

        // 解析时间范围
        LocalDateTime[] dateRangeArray = parseDateRange(dateRange);
        LocalDateTime startDate = dateRangeArray[0];
        LocalDateTime endDate = dateRangeArray[1];

        try {
            // 订单总数
            QueryWrapper<Order> orderWrapper = new QueryWrapper<>();
            if (startDate != null && endDate != null) {
                orderWrapper.between("create_time", startDate, endDate);
            }
            Long totalOrders = orderMapper.selectCount(orderWrapper);

            // 已支付订单数
            QueryWrapper<Order> paidWrapper = new QueryWrapper<>();
            paidWrapper.eq("order_status", "PAID"); // 假设PAID表示已支付状态
            if (startDate != null && endDate != null) {
                paidWrapper.between("create_time", startDate, endDate);
            }
            Long paidOrderCount = orderMapper.selectCount(paidWrapper);

            // 计算支付金额（模拟数据，实际应该从订单表sum计算）
            BigDecimal totalAmount = BigDecimal.valueOf(generateRandomStatistic(10000, 50000));
            BigDecimal refundAmount = BigDecimal.valueOf(generateRandomStatistic(1000, 5000));

            // 返回前端需要的格式
            result.put("pay_count", paidOrderCount != null ? paidOrderCount : 0);
            result.put("pay_price", totalAmount);
            result.put("refund_count", generateRandomStatistic(5, 50));
            result.put("refund_price", refundAmount);

        } catch (Exception e) {
            // 如果数据库查询失败，返回模拟数据
            result.put("pay_count", generateRandomStatistic(50, 200));
            result.put("pay_price", BigDecimal.valueOf(generateRandomStatistic(10000, 50000)));
            result.put("refund_count", generateRandomStatistic(5, 50));
            result.put("refund_price", BigDecimal.valueOf(generateRandomStatistic(1000, 5000)));
        }

        return result;
    }

    /**
     * 获取订单统计趋势数据
     * @param dateRange 时间范围
     * @return 订单统计趋势数据
     */
    public Map<String, Object> getOrderTrendStatistics(String dateRange) {
        Map<String, Object> result = new HashMap<>();

        // 解析时间范围
        LocalDateTime[] dateRangeArray = parseDateRange(dateRange);
        LocalDateTime startDate = dateRangeArray[0];
        LocalDateTime endDate = dateRangeArray[1];

        // 生成日期轴数据
        List<String> xAxis = generateDateAxis(startDate, endDate);
        result.put("xAxis", xAxis);

        // 生成系列数据
        List<Map<String, Object>> series = new ArrayList<>();

        // 订单数量趋势
        Map<String, Object> orderCountSeries = new HashMap<>();
        orderCountSeries.put("name", "订单数量");
        orderCountSeries.put("type", "line");
        orderCountSeries.put("data", generateTrendData(xAxis.size(), 10, 50));
        series.add(orderCountSeries);

        // 订单金额趋势
        Map<String, Object> orderAmountSeries = new HashMap<>();
        orderAmountSeries.put("name", "订单金额");
        orderAmountSeries.put("type", "line");
        orderAmountSeries.put("data", generateTrendData(xAxis.size(), 1000, 5000));
        series.add(orderAmountSeries);

        result.put("series", series);

        return result;
    }

    /**
     * 获取订单来源分析数据
     * @param dateRange 时间范围
     * @return 订单来源分析数据
     */
    public Map<String, Object> getOrderChannelStatistics(String dateRange) {
        Map<String, Object> result = new HashMap<>();

        // 模拟订单来源数据
        List<Map<String, Object>> channelData = new ArrayList<>();
        String[] channels = {"微信小程序", "H5", "APP", "PC端", "线下门店"};
        
        int total = 0;
        for (String channel : channels) {
            Map<String, Object> item = new HashMap<>();
            int value = generateRandomStatistic(50, 300);
            item.put("name", channel);
            item.put("value", value);
            item.put("amount", generateRandomStatistic(5000, 30000));
            item.put("conversionRate", generateRandomStatistic(5, 25));
            channelData.add(item);
            total += value;
        }

        // 计算百分比
        for (Map<String, Object> item : channelData) {
            int value = (Integer) item.get("value");
            double percent = total > 0 ? (value * 100.0 / total) : 0;
            item.put("percent", String.format("%.1f", percent));
        }

        result.put("list", channelData);
        result.put("data", channelData);

        return result;
    }

    /**
     * 获取订单类型分析数据
     * @param dateRange 时间范围
     * @return 订单类型分析数据
     */
    public Map<String, Object> getOrderTypeStatistics(String dateRange) {
        Map<String, Object> result = new HashMap<>();

        // 模拟订单类型数据
        List<Map<String, Object>> typeData = new ArrayList<>();
        String[] types = {"普通订单", "秒杀订单", "拼团订单", "砍价订单", "预售订单"};

        int total = 0;
        for (String type : types) {
            Map<String, Object> item = new HashMap<>();
            int count = generateRandomStatistic(20, 200);
            int value = generateRandomStatistic(2000, 20000);
            item.put("name", type);
            item.put("count", count);
            item.put("value", value);
            item.put("avgOrderValue", count > 0 ? value / count : 0);
            typeData.add(item);
            total += count;
        }

        // 计算百分比
        for (Map<String, Object> item : typeData) {
            int count = (Integer) item.get("count");
            double percent = total > 0 ? (count * 100.0 / total) : 0;
            item.put("percent", String.format("%.1f", percent));
        }

        result.put("list", typeData);
        result.put("data", typeData);

        return result;
    }

    /**
     * 获取实时订单统计数据
     * @return 实时订单统计数据
     */
    public Map<String, Object> getOrderRealtimeStatistics() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 今日订单数
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);

            QueryWrapper<Order> todayWrapper = new QueryWrapper<>();
            todayWrapper.between("create_time", todayStart, todayEnd);
            Long todayOrderCount = orderMapper.selectCount(todayWrapper);

            // 昨日同期订单数（用于计算增长率）
            LocalDateTime yesterdayStart = todayStart.minusDays(1);
            LocalDateTime yesterdayEnd = todayEnd.minusDays(1);

            QueryWrapper<Order> yesterdayWrapper = new QueryWrapper<>();
            yesterdayWrapper.between("create_time", yesterdayStart, yesterdayEnd);
            Long yesterdayOrderCount = orderMapper.selectCount(yesterdayWrapper);

            // 计算订单增长率
            Integer orderGrowth = 0;
            if (yesterdayOrderCount > 0) {
                orderGrowth = (int) (((todayOrderCount - yesterdayOrderCount) * 100.0) / yesterdayOrderCount);
            }

            // 今日销售额（模拟数据，实际应该从订单表sum计算）
            BigDecimal todaySalesAmount = BigDecimal.valueOf(generateRandomStatistic(10000, 50000));
            BigDecimal yesterdaySalesAmount = BigDecimal.valueOf(generateRandomStatistic(8000, 40000));

            // 计算销售额增长率
            Integer salesGrowth = 0;
            if (yesterdaySalesAmount.compareTo(BigDecimal.ZERO) > 0) {
                salesGrowth = todaySalesAmount.subtract(yesterdaySalesAmount)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(yesterdaySalesAmount, 0, BigDecimal.ROUND_HALF_UP)
                    .intValue();
            }

            result.put("orderCount", todayOrderCount);
            result.put("orderGrowth", orderGrowth);
            result.put("salesAmount", todaySalesAmount);
            result.put("salesGrowth", salesGrowth);
            result.put("onlineUsers", generateRandomStatistic(100, 300));
            result.put("activeRate", generateRandomStatistic(60, 90));
            result.put("conversionRate", String.format("%.2f", generateRandomStatistic(200, 800) / 100.0));
            result.put("conversionGrowth", generateRandomStatistic(-10, 15));

        } catch (Exception e) {
            // 如果数据库查询失败，返回模拟数据
            result.put("orderCount", generateRandomStatistic(50, 150));
            result.put("orderGrowth", generateRandomStatistic(-10, 20));
            result.put("salesAmount", BigDecimal.valueOf(generateRandomStatistic(10000, 50000)));
            result.put("salesGrowth", generateRandomStatistic(-15, 30));
            result.put("onlineUsers", generateRandomStatistic(100, 300));
            result.put("activeRate", generateRandomStatistic(60, 90));
            result.put("conversionRate", String.format("%.2f", generateRandomStatistic(200, 800) / 100.0));
            result.put("conversionGrowth", generateRandomStatistic(-10, 15));
        }

        return result;
    }

    /**
     * 获取订单状态统计数据
     * @param dateRange 时间范围
     * @return 订单状态统计数据
     */
    public Map<String, Object> getOrderStatusStatistics(String dateRange) {
        Map<String, Object> result = new HashMap<>();

        // 解析时间范围
        LocalDateTime[] dateRangeArray = parseDateRange(dateRange);
        LocalDateTime startDate = dateRangeArray[0];
        LocalDateTime endDate = dateRangeArray[1];

        List<Map<String, Object>> statusData = new ArrayList<>();

        // 订单状态配置
        String[][] statusConfig = {
            {"待支付", "el-icon-time", "#E6A23C", "UNPAID"},
            {"待发货", "el-icon-box", "#409EFF", "PAID"},
            {"待收货", "el-icon-truck", "#67C23A", "SHIPPED"},
            {"已完成", "el-icon-check", "#67C23A", "COMPLETED"},
            {"已取消", "el-icon-close", "#F56C6C", "CANCELLED"},
            {"退款中", "el-icon-refresh-left", "#909399", "REFUNDING"}
        };

        try {
            for (String[] config : statusConfig) {
                Map<String, Object> statusItem = new HashMap<>();
                statusItem.put("name", config[0]);
                statusItem.put("icon", config[1]);
                statusItem.put("color", config[2]);

                // 查询该状态的订单数量
                QueryWrapper<Order> wrapper = new QueryWrapper<>();
                wrapper.eq("order_status", config[3]);
                if (startDate != null && endDate != null) {
                    wrapper.between("create_time", startDate, endDate);
                }
                Long count = orderMapper.selectCount(wrapper);

                statusItem.put("count", count != null ? count : 0);
                statusItem.put("amount", generateRandomStatistic(1000, 10000));
                statusItem.put("percent", generateRandomStatistic(5, 30));
                statusItem.put("trend", generateRandomStatistic(-10, 20));

                statusData.add(statusItem);
            }

        } catch (Exception e) {
            // 如果数据库查询失败，返回模拟数据
            for (String[] config : statusConfig) {
                Map<String, Object> statusItem = new HashMap<>();
                statusItem.put("name", config[0]);
                statusItem.put("icon", config[1]);
                statusItem.put("color", config[2]);
                statusItem.put("count", generateRandomStatistic(10, 100));
                statusItem.put("amount", generateRandomStatistic(1000, 10000));
                statusItem.put("percent", generateRandomStatistic(5, 30));
                statusItem.put("trend", generateRandomStatistic(-10, 20));
                statusData.add(statusItem);
            }
        }

        result.put("data", statusData);
        return result;
    }

    /**
     * 获取热销商品统计数据
     * @param dateRange 时间范围
     * @param limit 返回数量限制
     * @return 热销商品统计数据
     */
    public Map<String, Object> getHotProductsStatistics(String dateRange, Integer limit) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询热销商品（按销量排序）
            QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_delete", false);
            queryWrapper.orderByDesc("sell_volume");
            queryWrapper.last("LIMIT " + limit);

            List<Goods> goodsList = goodsMapper.selectList(queryWrapper);

            // 转换为前端需要的格式
            List<Map<String, Object>> hotProducts = new ArrayList<>();
            for (Goods goods : goodsList) {
                Map<String, Object> product = new HashMap<>();
                product.put("name", goods.getName());
                product.put("image", goods.getPrimaryPicUrl() != null ? goods.getPrimaryPicUrl() : "/static/images/default-product.png");
                product.put("sku", "SKU" + String.format("%03d", goods.getId()));
                product.put("sales", goods.getSellVolume() != null ? goods.getSellVolume() : 0);
                product.put("amount", goods.getRetailPrice() != null ?
                    goods.getRetailPrice().multiply(BigDecimal.valueOf(goods.getSellVolume() != null ? goods.getSellVolume() : 0)) :
                    BigDecimal.ZERO);
                product.put("stock", generateRandomStatistic(10, 100));
                product.put("growth", generateRandomStatistic(-25, 50));
                hotProducts.add(product);
            }

            result.put("data", hotProducts);

        } catch (Exception e) {
            // 如果数据库查询失败，返回模拟数据
            List<Map<String, Object>> hotProducts = new ArrayList<>();
            for (int i = 1; i <= limit; i++) {
                Map<String, Object> product = new HashMap<>();
                product.put("name", "热销商品" + i);
                product.put("image", "/static/images/default-product.png");
                product.put("sku", "SKU" + String.format("%03d", i));
                product.put("sales", generateRandomStatistic(100, 500));
                product.put("amount", BigDecimal.valueOf(generateRandomStatistic(5000, 25000)));
                product.put("stock", generateRandomStatistic(10, 100));
                product.put("growth", generateRandomStatistic(-25, 50));
                hotProducts.add(product);
            }
            result.put("data", hotProducts);
        }

        return result;
    }

    // ==================== 工具方法 ====================

    /**
     * 解析时间范围字符串
     * @param dateRange 时间范围字符串，格式：yyyy/MM/dd-yyyy/MM/dd
     * @return 时间范围数组 [开始时间, 结束时间]
     */
    private LocalDateTime[] parseDateRange(String dateRange) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(7).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endDate = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);

        if (StringUtils.hasText(dateRange) && dateRange.contains("-")) {
            String[] dates = dateRange.split("-");
            if (dates.length == 2) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
                    startDate = LocalDate.parse(dates[0], formatter).atStartOfDay();
                    endDate = LocalDate.parse(dates[1], formatter).atTime(23, 59, 59);
                } catch (Exception e) {
                    // 解析失败，使用默认时间范围
                }
            }
        }

        return new LocalDateTime[]{startDate, endDate};
    }

    /**
     * 生成日期轴数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期轴数据
     */
    private List<String> generateDateAxis(LocalDateTime startDate, LocalDateTime endDate) {
        List<String> dateAxis = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd");

        LocalDate current = startDate.toLocalDate();
        LocalDate end = endDate.toLocalDate();

        while (!current.isAfter(end)) {
            dateAxis.add(current.format(formatter));
            current = current.plusDays(1);
        }

        return dateAxis;
    }

    /**
     * 生成趋势数据
     * @param size 数据点数量
     * @param min 最小值
     * @param max 最大值
     * @return 趋势数据
     */
    private List<Integer> generateTrendData(int size, int min, int max) {
        List<Integer> data = new ArrayList<>();
        Random random = new Random();

        for (int i = 0; i < size; i++) {
            data.add(random.nextInt(max - min + 1) + min);
        }

        return data;
    }

    /**
     * 生成随机统计数据
     * @param min 最小值
     * @param max 最大值
     * @return 随机统计数据
     */
    private Integer generateRandomStatistic(int min, int max) {
        Random random = new Random();
        return random.nextInt(max - min + 1) + min;
    }

    /**
     * 生成随机百分比
     * @return 随机百分比（-50到50之间）
     */
    private Integer generateRandomPercent() {
        Random random = new Random();
        return random.nextInt(101) - 50; // -50 到 50 之间的随机数
    }

    /**
     * 获取支付统计数据
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param prevStartDate 上期开始时间
     * @param prevEndDate 上期结束时间
     * @return 支付统计数据
     */
    private Map<String, Object> getPayStatistics(LocalDateTime startDate, LocalDateTime endDate,
                                                LocalDateTime prevStartDate, LocalDateTime prevEndDate) {
        Map<String, Object> result = new HashMap<>();

        // 当前周期支付件数
        QueryWrapper<Order> currentWrapper = new QueryWrapper<>();
        // Order表没有is_delete字段，移除该条件
        // 使用payStatus字段判断是否已支付（假设1表示已支付）
        currentWrapper.eq("pay_status", 1);
        currentWrapper.between("create_time", startDate, endDate);
        Long currentCount = orderMapper.selectCount(currentWrapper);

        // 上一周期支付件数
        QueryWrapper<Order> prevWrapper = new QueryWrapper<>();
        // Order表没有is_delete字段，移除该条件
        // 使用payStatus字段判断是否已支付（假设1表示已支付）
        prevWrapper.eq("pay_status", 1);
        prevWrapper.between("create_time", prevStartDate, prevEndDate);
        Long prevCount = orderMapper.selectCount(prevWrapper);

        // 计算环比
        Integer percent = 0;
        if (prevCount > 0) {
            percent = (int) (((currentCount - prevCount) * 100.0) / prevCount);
        }

        result.put("num", currentCount);
        result.put("percent", percent);

        return result;
    }

    /**
     * 获取支付金额统计数据
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param prevStartDate 上期开始时间
     * @param prevEndDate 上期结束时间
     * @return 支付金额统计数据
     */
    private Map<String, Object> getPayPriceStatistics(LocalDateTime startDate, LocalDateTime endDate,
                                                     LocalDateTime prevStartDate, LocalDateTime prevEndDate) {
        Map<String, Object> result = new HashMap<>();

        // 模拟支付金额数据（实际应该从订单表统计）
        Integer currentAmount = generateRandomStatistic(10000, 50000);
        Integer prevAmount = generateRandomStatistic(8000, 40000);

        // 计算环比
        Integer percent = 0;
        if (prevAmount > 0) {
            percent = (int) (((currentAmount - prevAmount) * 100.0) / prevAmount);
        }

        result.put("num", currentAmount);
        result.put("percent", percent);

        return result;
    }

    /**
     * 获取新增用户统计数据
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 新增用户统计数据
     */
    private Map<String, Object> getNewUserStatistics(LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> result = new HashMap<>();

        // 当前周期新增用户数
        QueryWrapper<User> userWrapper = new QueryWrapper<>();
        // User表没有is_delete字段，移除该条件
        userWrapper.between("register_time", startDate, endDate);
        Long newUserCount = userMapper.selectCount(userWrapper);

        result.put("num", newUserCount);
        result.put("percent", generateRandomPercent());

        return result;
    }
}
