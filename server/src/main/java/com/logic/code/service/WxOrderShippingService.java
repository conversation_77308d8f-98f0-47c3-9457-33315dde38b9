package com.logic.code.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.logic.code.config.WxMaConfiguration;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.WxShippingInfo;
import com.logic.code.model.dto.WxShippingInfoDTO;
import com.logic.code.model.vo.WxOrderInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序发货信息管理服务
 * 实现微信官方发货信息管理API
 */
@Slf4j
@Service
public class WxOrderShippingService {

    @Value("${wx.miniapp.configs[0].appid}")
    private String appid;

    @Resource
    private WxShippingInfoService wxShippingInfoService;

    @Resource
    private OrderService orderService;

    // 微信API基础URL
    private static final String WX_API_BASE_URL = "https://api.weixin.qq.com/wxa/sec/order";

    /**
     * 发货信息录入接口
     * 对应微信API: upload_shipping_info
     */
    public boolean uploadShippingInfo(WxShippingInfoDTO shippingInfoDTO) {
        try {
            // 获取access_token
            String accessToken = getAccessToken();
            
            // 构建请求URL
            String url = WX_API_BASE_URL + "/upload_shipping_info?access_token=" + accessToken;
            
            // 构建请求参数
            Map<String, Object> requestData = buildShippingInfoRequest(shippingInfoDTO);
            
            // 发送请求
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(requestData))
                    .execute();
            
            // 处理响应
            JSONObject result = JSONUtil.parseObj(response.body());
            log.info("微信发货信息录入响应: {}", result);
            
            if (result.getInt("errcode") == 0) {
                // 保存发货信息到数据库
                saveShippingInfo(shippingInfoDTO, 1, null);
                return true;
            } else {
                // 保存失败信息
                saveShippingInfo(shippingInfoDTO, 2, result.getStr("errmsg"));
                log.error("微信发货信息录入失败: {}", result.getStr("errmsg"));
                return false;
            }
            
        } catch (Exception e) {
            log.error("发货信息录入异常", e);
            saveShippingInfo(shippingInfoDTO, 2, e.getMessage());
            return false;
        }
    }

    /**
     * 查询订单发货状态
     * 对应微信API: get_order
     */
    public WxOrderInfoVO getOrderInfo(String transactionId, String merchantId, String merchantTradeNo) {
        try {
            // 获取access_token
            String accessToken = getAccessToken();
            
            // 构建请求URL
            String url = WX_API_BASE_URL + "/get_order?access_token=" + accessToken;
            
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            if (transactionId != null) {
                requestData.put("transaction_id", transactionId);
            }
            if (merchantId != null) {
                requestData.put("merchant_id", merchantId);
            }
            if (merchantTradeNo != null) {
                requestData.put("merchant_trade_no", merchantTradeNo);
            }
            
            // 发送请求
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(requestData))
                    .execute();
            
            // 处理响应
            JSONObject result = JSONUtil.parseObj(response.body());
            log.info("微信查询订单发货状态响应: {}", result);
            
            if (result.getInt("errcode") == 0) {
                return parseOrderInfo(result.getJSONObject("order"));
            } else {
                log.error("查询订单发货状态失败: {}", result.getStr("errmsg"));
                return null;
            }
            
        } catch (Exception e) {
            log.error("查询订单发货状态异常", e);
            return null;
        }
    }

    /**
     * 确认收货提醒接口
     * 对应微信API: notify_confirm_receive
     */
    public boolean notifyConfirmReceive(String transactionId, String merchantId, 
                                      String merchantTradeNo, Date receivedTime) {
        try {
            // 获取access_token
            String accessToken = getAccessToken();
            
            // 构建请求URL
            String url = WX_API_BASE_URL + "/notify_confirm_receive?access_token=" + accessToken;
            
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            if (transactionId != null) {
                requestData.put("transaction_id", transactionId);
            }
            if (merchantId != null) {
                requestData.put("merchant_id", merchantId);
            }
            if (merchantTradeNo != null) {
                requestData.put("merchant_trade_no", merchantTradeNo);
            }
            requestData.put("received_time", receivedTime.getTime() / 1000); // 转换为秒级时间戳
            
            // 发送请求
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(requestData))
                    .execute();
            
            // 处理响应
            JSONObject result = JSONUtil.parseObj(response.body());
            log.info("微信确认收货提醒响应: {}", result);
            
            if (result.getInt("errcode") == 0) {
                return true;
            } else {
                log.error("确认收货提醒失败: {}", result.getStr("errmsg"));
                return false;
            }
            
        } catch (Exception e) {
            log.error("确认收货提醒异常", e);
            return false;
        }
    }

    /**
     * 消息跳转路径设置接口
     * 对应微信API: set_msg_jump_path
     */
    public boolean setMsgJumpPath(String path) {
        try {
            // 获取access_token
            String accessToken = getAccessToken();
            
            // 构建请求URL
            String url = WX_API_BASE_URL + "/set_msg_jump_path?access_token=" + accessToken;
            
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("path", path);
            
            // 发送请求
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(requestData))
                    .execute();
            
            // 处理响应
            JSONObject result = JSONUtil.parseObj(response.body());
            log.info("微信消息跳转路径设置响应: {}", result);
            
            return result.getInt("errcode") == 0;
            
        } catch (Exception e) {
            log.error("消息跳转路径设置异常", e);
            return false;
        }
    }

    /**
     * 查询小程序是否已开通发货信息管理服务
     * 对应微信API: is_trade_managed
     */
    public Boolean isTradeManaged() {
        try {
            // 获取access_token
            String accessToken = getAccessToken();
            
            // 构建请求URL
            String url = WX_API_BASE_URL + "/is_trade_managed?access_token=" + accessToken;
            
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("appid", appid);
            
            // 发送请求
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(requestData))
                    .execute();
            
            // 处理响应
            JSONObject result = JSONUtil.parseObj(response.body());
            log.info("微信查询服务开通状态响应: {}", result);
            
            if (result.getInt("errcode") == 0) {
                return result.getBool("is_trade_managed");
            } else {
                log.error("查询服务开通状态失败: {}", result.getStr("errmsg"));
                return null;
            }
            
        } catch (Exception e) {
            log.error("查询服务开通状态异常", e);
            return null;
        }
    }

    /**
     * 获取微信access_token
     */
    private String getAccessToken() throws Exception {
        WxMaService wxService = WxMaConfiguration.getMaService(appid);
        return wxService.getAccessToken();
    }

    /**
     * 构建发货信息请求参数
     */
    private Map<String, Object> buildShippingInfoRequest(WxShippingInfoDTO dto) {
        Map<String, Object> requestData = new HashMap<>();
        
        // 订单信息
        requestData.put("order_key", dto.getOrderKey());
        requestData.put("logistics_type", dto.getLogisticsType());
        requestData.put("delivery_mode", dto.getDeliveryMode());
        
        if (dto.getIsAllDelivered() != null) {
            requestData.put("is_all_delivered", dto.getIsAllDelivered());
        }
        
        requestData.put("shipping_list", dto.getShippingList());
        requestData.put("upload_time", DateUtil.format(new Date(), "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
        requestData.put("payer", dto.getPayer());
        
        return requestData;
    }

    /**
     * 保存发货信息到数据库
     */
    private void saveShippingInfo(WxShippingInfoDTO dto, Integer wxStatus, String errorMsg) {
        try {
            WxShippingInfo shippingInfo = WxShippingInfo.builder()
                    .transactionId(dto.getOrderKey().getTransactionId())
                    .mchid(dto.getOrderKey().getMchid())
                    .outTradeNo(dto.getOrderKey().getOutTradeNo())
                    .openid(dto.getPayer().getOpenid())
                    .logisticsType(dto.getLogisticsType())
                    .deliveryMode(dto.getDeliveryMode())
                    .isAllDelivered(dto.getIsAllDelivered())
                    .shippingList(JSONUtil.toJsonStr(dto.getShippingList()))
                    .uploadTime(new Date())
                    .wxStatus(wxStatus)
                    .wxErrorMsg(errorMsg)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .build();
            
            wxShippingInfoService.insert(shippingInfo);
        } catch (Exception e) {
            log.error("保存发货信息到数据库失败", e);
        }
    }

    /**
     * 解析微信订单信息响应
     */
    private WxOrderInfoVO parseOrderInfo(JSONObject orderJson) {
        WxOrderInfoVO orderInfo = new WxOrderInfoVO();
        
        orderInfo.setTransactionId(orderJson.getStr("transaction_id"));
        orderInfo.setMerchantId(orderJson.getStr("merchant_id"));
        orderInfo.setSubMerchantId(orderJson.getStr("sub_merchant_id"));
        orderInfo.setMerchantTradeNo(orderJson.getStr("merchant_trade_no"));
        orderInfo.setDescription(orderJson.getStr("description"));
        orderInfo.setPaidAmount(orderJson.getInt("paid_amount"));
        orderInfo.setOpenid(orderJson.getStr("openid"));
        orderInfo.setTradeCreateTime(orderJson.getLong("trade_create_time"));
        orderInfo.setPayTime(orderJson.getLong("pay_time"));
        orderInfo.setOrderState(orderJson.getInt("order_state"));
        orderInfo.setInComplaint(orderJson.getBool("in_complaint"));
        
        // 解析发货信息
        JSONObject shippingJson = orderJson.getJSONObject("shipping");
        if (shippingJson != null) {
            WxOrderInfoVO.ShippingInfo shipping = new WxOrderInfoVO.ShippingInfo();
            shipping.setDeliveryMode(shippingJson.getInt("delivery_mode"));
            shipping.setLogisticsType(shippingJson.getInt("logistics_type"));
            shipping.setFinishShipping(shippingJson.getBool("finish_shipping"));
            shipping.setGoodsDesc(shippingJson.getStr("goods_desc"));
            shipping.setFinishShippingCount(shippingJson.getInt("finish_shipping_count"));
            
            // TODO: 解析shipping_list
            
            orderInfo.setShipping(shipping);
        }
        
        return orderInfo;
    }
}
