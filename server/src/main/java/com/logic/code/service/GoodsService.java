package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.response.CommonResultStatus;
import com.logic.code.common.utils.GoodsExcelUtils;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.common.utils.StringUtils;
import com.logic.code.entity.*;
import com.logic.code.entity.goods.*;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.GoodsMapper;
import com.logic.code.model.dto.GoodsAttributeDTO;
import com.logic.code.model.dto.GoodsImportDTO;
import com.logic.code.model.dto.GoodsImportResultDTO;
import com.logic.code.model.dto.GoodsSpecificationDTO;
import com.logic.code.model.query.GoodsSearchQuery;
import com.logic.code.model.query.admin.GoodsParam;
import com.logic.code.model.vo.*;
import com.logic.code.model.vo.admin.GoodsVO;
import jakarta.annotation.Resource;
import logic.orm.utils.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 * @date 2025/5/7 22:40
 * @desc
 */
@Service
@Slf4j
public class GoodsService extends BaseService<Goods> {

    @Resource
    private GoodsMapper goodsMapper;

    @Override
    protected CommonMapper<Goods> getMapper() {
        return goodsMapper;
    }


    @Resource
    private CategoryService categoryService;

    @Autowired
    private GoodsGalleryService goodsGalleryService;

    @Autowired
    private GoodsAttributeService goodsAttributeService;

    @Autowired
    private GoodsIssueService goodsIssueService;

    @Autowired
    private BrandService brandService;

    @Autowired
    private CommentService commentService;

    @Autowired
    private CommentPictureService commentPictureService;

    @Autowired
    private UserService userService;

    @Autowired
    private CollectService collectService;

    @Autowired
    private GoodsSpecificationService goodsSpecificationService;

    @Autowired
    private ProductService productService;

    @Autowired
    private RelatedGoodsService relatedGoodsService;

    @Resource
    private FootprintService footprintService;

    @Autowired
    private SpecificationService specificationService;

    @Autowired
    private SpecificationTemplateService specificationTemplateService;


    private static final String PRODUCT_CACHE_KEY = "product_cache";

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Get goods page with pagination
     */
    public Page getGoodsPage(GoodsParam param) {
        Page<Goods> page = new Page<>(param.getPage(), param.getLimit());
        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();

        // 根据商品状态类型过滤
        if (param.getType() != null) {
            switch (param.getType()) {
                case 1: // 全部商品
                    queryWrapper.eq("is_delete", false);
                    break;
                case 2: // 出售中
                    queryWrapper.eq("is_delete", false);
                    queryWrapper.eq("is_on_sale", true);
                    break;
                case 3: // 仓库中
                    queryWrapper.eq("is_delete", false);
                    queryWrapper.eq("is_on_sale", false);
                    break;
                case 4: // 回收站
                    queryWrapper.eq("is_delete", true);
                    break;
                default:
                    queryWrapper.eq("is_delete", false);
                    break;
            }
        } else {
            // 默认不显示已删除的商品
            queryWrapper.eq("is_delete", false);
        }

        if (StringUtils.isNotBlank(param.getStoreName())) {
            queryWrapper.like("name", param.getStoreName());
        }

        if (param.getCategoryId() != null) {
            queryWrapper.eq("category_id", param.getCategoryId());
        }

        if (param.getIsOnSale() != null) {
            queryWrapper.eq("is_on_sale", param.getIsOnSale());
        }

        queryWrapper.orderByDesc("create_time");

        return goodsMapper.selectPage(page, queryWrapper);
    }

    /**
     * Create new product
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "goodsPageInfo", allEntries = true)
    public Goods createGoods(GoodsVO goodsVO) {
        // Convert VO to entity
        log.info("createGoods:{}", goodsVO);
        Goods goods = new Goods();
        goods.setName(goodsVO.getStoreName());

        if (goodsVO.getCategoryIds() != null && !goodsVO.getCategoryIds().isEmpty()) {
            goods.setCategoryId(goodsVO.getCategoryIds().get(goodsVO.getCategoryIds().size() - 1));
        } else {
            throw new RuntimeException("商品分类不能为空");
        }

        goods.setGoodsBrief(goodsVO.getStoreInfo());
        goods.setIsOnSale(goodsVO.getIsShow() == 1);
        goods.setListPicUrl(goodsVO.getImage());
        goods.setPrimaryPicUrl(goodsVO.getImage());
        goods.setGoodsUnit(goodsVO.getUnitName());
        goods.setRetailPrice(getMinPrice(goodsVO.getAttrs()));
        goods.setGoodsDesc(goodsVO.getDescription());
        goods.setIsHot(goodsVO.getIsHot() == 1);
        goods.setIsNewly(goodsVO.getIsNew() == 1);
        goods.setKeywords(goodsVO.getKeyword());
        goods.setSortOrder(goodsVO.getSort().shortValue());
        goods.setSellVolume(goodsVO.getVirtualSales());
        goods.setGoodsNumber(getTotalStock(goodsVO.getAttrs()));
        goods.setPromotionDesc("");
        goods.setPromotionTag("");
        goods.setAppExclusivePrice(BigDecimal.ZERO);
        goods.setIsAppExclusive(false);
        goods.setIsLimited(false);
        goods.setCreateTime(new Date());
        goods.setIsDelete(false);

        // Set unit price
        if (goodsVO.getUnitPrice() != null) {
            goods.setUnitPrice(goodsVO.getUnitPrice());
        } else {
            goods.setUnitPrice(BigDecimal.ZERO);
        }

        // Set display systems
        if (goodsVO.getDisplaySystems() != null && !goodsVO.getDisplaySystems().isEmpty()) {
            goods.setDisplaySystems(convertDisplaySystemsToJson(goodsVO.getDisplaySystems()));
        }

        // Set detail tag
        goods.setDetailTag(goodsVO.getDetailTag());

        // Save goods
        goodsMapper.insert(goods);

        saveGoodsGallery(goods.getId(), goodsVO.getSliderImages());

        saveSpecificationsAndProducts(goods.getId(), goodsVO);

        saveGoodsAttributes(goods.getId(), goodsVO.getParamsList());

        cacheProduct(goods.getId(), goodsVO);

        return goods;
    }

    /**
     * Update existing product
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "goodsPageInfo", allEntries = true)
    public Goods updateGoods(GoodsVO goodsVO) {
        log.info("updateGoods:{}", goodsVO);
        Goods goods = queryById(goodsVO.getId());
        if (goods == null) {
            throw new RuntimeException("Product not found");
        }

        // Update goods
        goods.setName(goodsVO.getStoreName());
        goods.setCategoryId(goodsVO.getCategoryIds().get(goodsVO.getCategoryIds().size() - 1));
        goods.setGoodsBrief(goodsVO.getStoreInfo());
        goods.setIsOnSale(goodsVO.getIsShow() == 1);
        goods.setListPicUrl(goodsVO.getImage());
        goods.setPrimaryPicUrl(goodsVO.getImage());
        goods.setGoodsUnit(goodsVO.getUnitName());
        goods.setRetailPrice(getMinPrice(goodsVO.getAttrs()));
        goods.setGoodsDesc(goodsVO.getDescription());
        goods.setIsHot(goodsVO.getIsHot() == 1);
        goods.setIsNewly(goodsVO.getIsNew() == 1);
        goods.setKeywords(goodsVO.getKeyword());
        goods.setSortOrder(goodsVO.getSort().shortValue());
        goods.setSellVolume(goodsVO.getVirtualSales());
        goods.setGoodsNumber(getTotalStock(goodsVO.getAttrs()));

        // Set unit price
        if (goodsVO.getUnitPrice() != null) {
            goods.setUnitPrice(goodsVO.getUnitPrice());
        } else {
            goods.setUnitPrice(BigDecimal.ZERO);
        }

        // Set display systems
        if (goodsVO.getDisplaySystems() != null && !goodsVO.getDisplaySystems().isEmpty()) {
            goods.setDisplaySystems(convertDisplaySystemsToJson(goodsVO.getDisplaySystems()));
        }

        // Set detail tag
        goods.setDetailTag(goodsVO.getDetailTag());

        // Update goods
        updateById(goods);

        // Delete existing gallery, specifications, and products
        goodsGalleryService.deleteByGoodsId(goods.getId());
        List<GoodsSpecification> goodsSpecifications = goodsSpecificationService.queryByGoodsId(goods.getId());
        if (ListUtils.isNotBlank(goodsSpecifications)) {
            goodsSpecifications.forEach(t -> {
                t.setIsDeleted(true);
                t.setGoodsId(0);
                goodsSpecificationService.updateById(t);
            });
        }
        //goodsSpecificationService.deleteByGoodsId(goods.getId());
        productService.deleteByGoodsId(goods.getId());
        goodsAttributeService.deleteByGoodsId(goods.getId());

        // Save goods gallery (slider images)
        saveGoodsGallery(goods.getId(), goodsVO.getSliderImages());

        // Save goods specifications and products
        saveSpecificationsAndProducts(goods.getId(), goodsVO);

        // Save goods attributes
        saveGoodsAttributes(goods.getId(), goodsVO.getParamsList());

        // Clear product cache
        clearProductCache();

        return goods;
    }

    /**
     * Save goods gallery (slider images)
     */
    private void saveGoodsGallery(Integer goodsId, List<String> sliderImages) {
        if (sliderImages != null && !sliderImages.isEmpty()) {
            List<GoodsGallery> galleries = new ArrayList<>();
            for (int i = 0; i < sliderImages.size(); i++) {
                GoodsGallery gallery = new GoodsGallery();
                gallery.setGoodsId(goodsId);
                gallery.setImgUrl(sliderImages.get(i));
                gallery.setSortOrder(i);
                galleries.add(gallery);
            }
            goodsGalleryService.createBatch(galleries);
        }
    }

    /**
     * Save goods specifications and products with enhanced spec-stock data
     */
    private void saveSpecificationsAndProducts(Integer goodsId, GoodsVO goodsVO) {
        if (goodsVO.getSpecType() == 0) {
            // 单规格商品
            saveSingleSpecProduct(goodsId, goodsVO);
        } else {
            // 多规格商品
            saveMultiSpecProducts(goodsId, goodsVO);
        }
    }

    /**
     * 保存单规格商品
     */
    private void saveSingleSpecProduct(Integer goodsId, GoodsVO goodsVO) {
        if (goodsVO.getAttrs() != null && !goodsVO.getAttrs().isEmpty()) {
            Map<String, Object> attr = goodsVO.getAttrs().get(0);

            Product product = Product.builder()
                    .goodsId(goodsId)
                    .goodsSn(generateGoodsSn())
                    .goodsNumber(getIntegerValue(attr, "stock"))
                    .retailPrice(getBigDecimalValue(attr, "price"))
                    .costPrice(getBigDecimalValue(attr, "cost"))
                    .otPrice(getBigDecimalValue(attr, "ot_price"))
                    .barCode(getStringValue(attr, "bar_code"))
                    .barCodeNumber(getStringValue(attr, "bar_code"))
                    .goodsWeight(getBigDecimalValue(attr, "weight"))
                    .goodsVolume(getBigDecimalValue(attr, "volume"))
                    .picUrl(getStringValue(attr, "pic"))
                    .brokerage(getBigDecimalValue(attr, "brokerage"))
                    .brokerageTwo(getBigDecimalValue(attr, "brokerage_two"))
                    .quota(getIntegerValue(attr, "quota"))
                    .quotaShow(getBooleanValue(attr, "quota_show"))
                    .build();

            productService.create(product);
        }
    }

    /**
     * 保存多规格商品
     */
    private void saveMultiSpecProducts(Integer goodsId, GoodsVO goodsVO) {
        // 1. 保存规格定义
        List<GoodsSpecification> specifications = saveSpecificationDefinitions(goodsId, goodsVO);

        // 2. 保存产品SKU
        saveProductSKUs(goodsId, goodsVO, specifications);
    }

    /**
     * 保存规格定义
     */
    private List<GoodsSpecification> saveSpecificationDefinitions(Integer goodsId, GoodsVO goodsVO) {
        List<GoodsSpecification> specifications = new ArrayList<>();

        if (goodsVO.getItems() != null && !goodsVO.getItems().isEmpty()) {
            for (Map<String, Object> item : goodsVO.getItems()) {
                String specName = item.get("value").toString();

                // 确保规格类型存在
                Specification specification = getOrCreateSpecification(specName);

                List<Map<String, Object>> details = (List<Map<String, Object>>) item.get("detail");
                if (details != null) {
                    for (Map<String, Object> detail : details) {
                        GoodsSpecification goodsSpec = GoodsSpecification.builder()
                                .goodsId(goodsId)
                                .specificationId(specification.getId())
                                .value(detail.get("value").toString())
                                .picUrl(getStringValue(detail, "pic"))
                                .build();
                        specifications.add(goodsSpec);
                    }
                }
            }

            if (!specifications.isEmpty()) {
                goodsSpecificationService.createBatch(specifications);
            }
        }

        return specifications;
    }

    /**
     * 保存产品SKU
     */
    private void saveProductSKUs(Integer goodsId, GoodsVO goodsVO, List<GoodsSpecification> specifications) {
        if (goodsVO.getAttrs() != null && !goodsVO.getAttrs().isEmpty()) {
            List<Product> products = new ArrayList<>();

            for (Map<String, Object> attr : goodsVO.getAttrs()) {
                Product product = Product.builder()
                        .goodsId(goodsId)
                        .goodsSn(generateGoodsSn())
                        .goodsNumber(getIntegerValue(attr, "stock"))
                        .retailPrice(getBigDecimalValue(attr, "price"))
                        .costPrice(getBigDecimalValue(attr, "cost"))
                        .otPrice(getBigDecimalValue(attr, "ot_price"))
                        .barCode(getStringValue(attr, "bar_code"))
                        .barCodeNumber(getStringValue(attr, "bar_code"))
                        .goodsWeight(getBigDecimalValue(attr, "weight"))
                        .goodsVolume(getBigDecimalValue(attr, "volume"))
                        .picUrl(getStringValue(attr, "pic"))
                        .brokerage(getBigDecimalValue(attr, "brokerage"))
                        .brokerageTwo(getBigDecimalValue(attr, "brokerage_two"))
                        .quota(getIntegerValue(attr, "quota"))
                        .quotaShow(getBooleanValue(attr, "quota_show"))
                        .build();

                // 构建规格ID关联
                String specIds = buildSpecificationIds(attr, specifications);
                product.setGoodsSpecificationIds(specIds);

                products.add(product);
            }

            if (!products.isEmpty()) {
                productService.createBatch(products);
            }
        }
    }

    /**
     * 构建规格ID关联字符串
     */
    private String buildSpecificationIds(Map<String, Object> attr, List<GoodsSpecification> specifications) {
        List<String> specIds = new ArrayList<>();

        // 从attr的detail字段中获取规格值
        Object detailObj = attr.get("detail");
        if (detailObj instanceof Map) {
            Map<String, Object> detail = (Map<String, Object>) detailObj;

            for (GoodsSpecification spec : specifications) {
                for (Object value : detail.values()) {
                    if (spec.getValue().equals(value.toString())) {
                        specIds.add(spec.getId().toString());
                        break;
                    }
                }
            }
        }

        return String.join("_", specIds);
    }

    /**
     * 获取或创建规格类型
     */
    private Specification getOrCreateSpecification(String specName) {
        return specificationService.getOrCreate(specName);
    }

    /**
     * 根据规格组合查找产品
     *
     * @param goodsId          商品ID
     * @param specificationIds 规格ID组合，用下划线分隔
     * @return 匹配的产品信息
     */
    public Product findProductBySpecification(Integer goodsId, String specificationIds) {
        if (specificationIds == null || specificationIds.trim().isEmpty()) {
            // 如果没有规格ID，返回该商品的第一个产品
            Product query = new Product();
            query.setGoodsId(goodsId);
            List<Product> products = productService.queryList(query);
            return products.isEmpty() ? null : products.get(0);
        }

        // 根据规格ID组合查找产品
        Product query = new Product();
        query.setGoodsId(goodsId);
        query.setGoodsSpecificationIds(specificationIds);
        return productService.queryOne(query);
    }


    // 辅助方法：安全获取各种类型的值
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return 0;
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    private BigDecimal getBigDecimalValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return BigDecimal.ZERO;
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    private Boolean getBooleanValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return false;
        if (value instanceof Boolean) return (Boolean) value;
        return "1".equals(value.toString()) || "true".equalsIgnoreCase(value.toString());
    }

    /**
     * Save goods attributes
     */
    private void saveGoodsAttributes(Integer goodsId, List<Map<String, String>> paramsList) {
        if (paramsList != null && !paramsList.isEmpty()) {
            List<GoodsAttribute> attributes = new ArrayList<>();

            for (Map<String, String> param : paramsList) {
                GoodsAttribute attribute = new GoodsAttribute();
                attribute.setGoodsId(goodsId);
                attribute.setValue(param.get("value"));
                attribute.setAttributeValue(param.get("value"));
                attribute.setAttributeName(param.get("name"));

                // 保存属性ID（编码）
                String attributeIdStr = param.get("attributeId");
                if (attributeIdStr != null && !attributeIdStr.isEmpty()) {
                    try {
                        attribute.setAttributeId(Integer.valueOf(attributeIdStr));
                    } catch (NumberFormatException e) {
                        log.warn("Invalid attributeId format: {}", attributeIdStr);
                    }
                }

                attributes.add(attribute);
            }

            goodsAttributeService.createBatch(attributes);
        }
    }

    /**
     * Generate a unique goods SN
     */
    private String generateGoodsSn() {
        return "G" + System.currentTimeMillis();
    }

    /**
     * Get minimum price from attributes
     */
    private BigDecimal getMinPrice(List<Map<String, Object>> attrs) {
        if (attrs == null || attrs.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return attrs.stream().filter(attr -> attr.get("price") != null)
                .map(attr -> new BigDecimal(attr.get("price").toString()))
                .min(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * Get total stock from attributes
     */
    private Integer getTotalStock(List<Map<String, Object>> attrs) {
        if (attrs == null || attrs.isEmpty()) {
            return 0;
        }

        return attrs.stream().filter(attr -> attr.get("stock") != null)
                .mapToInt(attr -> Integer.valueOf(attr.get("stock").toString()))
                .sum();
    }

    /**
     * Cache product data
     */
    private void cacheProduct(Integer goodsId, GoodsVO goodsVO) {
        // Implementation depends on your caching strategy
        // This is a simple in-memory cache for demonstration
        Map<String, Object> cache = new HashMap<>();
        cache.put("id", goodsId);
        cache.put("data", goodsVO);

        // Store in cache
        // In a real implementation, you might use Redis or another caching solution
    }

    /**
     * Get product cache
     */
    public Map<String, Object> getProductCache() {
        // Implementation depends on your caching strategy
        // This is a placeholder implementation
        Map<String, Object> cache = new HashMap<>();
        cache.put("info", new HashMap<>());
        return cache;
    }

    /**
     * Clear product cache
     */
    public void clearProductCache() {
        // Implementation depends on your caching strategy
        // This is a placeholder implementation
    }

    /**
     * Get product rules (specifications)
     */
    public List<Map<String, Object>> getProductRules() {
        // 调用规格模板服务获取模板列表
        return specificationTemplateService.getTemplateList(null, 1, 100);
    }

    /**
     * Get freight templates
     */
    public List<Map<String, Object>> getFreightTemplates() {
        // Implementation depends on your business logic
        // This is a placeholder implementation
        return new ArrayList<>();
    }

    /**
     * Check if a product can change its activity type
     */
    public boolean checkProductActivity(Integer id) {
        // Implementation depends on your business logic
        // This is a placeholder implementation
        return true;
    }

    public GoodsResultVO queryList(GoodsSearchQuery goodsSearchQuery) {
        //没传分类id就查全部
        Criteria<Goods, Object> criteria = Criteria.of(Goods.class);
        if (goodsSearchQuery.getCategoryId() == null) {
            goodsSearchQuery.setCategoryId(0);
        }

        // 添加基础过滤条件：只查询未删除的商品
        criteria.andEqualTo(Goods::getIsDelete, false);

        if (goodsSearchQuery.getBrandId() != null) {
            criteria.andEqualTo(Goods::getBrandId, goodsSearchQuery.getBrandId());
        }
        if (StringUtils.isNotBlank(goodsSearchQuery.getKeyword())) {
            criteria.andLike(Goods::getName, "%" + goodsSearchQuery.getKeyword() + "%");
        }
        if (goodsSearchQuery.getNewly() != null) {
            criteria.andEqualTo(Goods::getIsNewly, goodsSearchQuery.getNewly());
        }
        if (goodsSearchQuery.getHot() != null) {
            criteria.andEqualTo(Goods::getIsHot, goodsSearchQuery.getHot());
        }
        criteria.fields(Goods::getCategoryId);
        List<Integer> categoryIds = queryByCriteria(criteria).stream()
                .map(Goods::getCategoryId)
                .collect(Collectors.toList());

        if (categoryIds.isEmpty()) {
            return GoodsResultVO.EMPTY_GOODS_RESULT;
        }
        //查询二级分类的parentIds
        List<Integer> parentIds = categoryService.queryParentIdsByIdIn(categoryIds);
        //一级分类
        List<CategoryFilterVO> categoryFilter = new LinkedList<CategoryFilterVO>() {{
            add(new CategoryFilterVO(0, "全部", false));
            addAll(categoryService.queryByIdIn(parentIds).stream()
                    .map(CategoryFilterVO::new)
                    .collect(Collectors.toList()));
        }};

        categoryFilter.forEach(categoryFilterDTO -> categoryFilterDTO.setChecked(categoryFilterDTO.getId().equals(goodsSearchQuery.getCategoryId())));

        // 创建新的criteria对象用于最终查询，避免重用之前的criteria
        Criteria<Goods, Object> finalCriteria = Criteria.of(Goods.class);

        // 添加基础过滤条件：只查询未删除的商品
        finalCriteria.andEqualTo(Goods::getIsDelete, false);

        // 重新应用所有过滤条件
        if (goodsSearchQuery.getBrandId() != null) {
            finalCriteria.andEqualTo(Goods::getBrandId, goodsSearchQuery.getBrandId());
        }
        if (StringUtils.isNotBlank(goodsSearchQuery.getKeyword())) {
            finalCriteria.andLike(Goods::getName, "%" + goodsSearchQuery.getKeyword() + "%");
        }
        if (goodsSearchQuery.getNewly() != null) {
            finalCriteria.andEqualTo(Goods::getIsNewly, goodsSearchQuery.getNewly());
        }
        if (goodsSearchQuery.getHot() != null) {
            finalCriteria.andEqualTo(Goods::getIsHot, goodsSearchQuery.getHot());
        }

        // 应用分类过滤条件
        if (goodsSearchQuery.getCategoryId() != null && goodsSearchQuery.getCategoryId() > 0) {
            //根据一级分类ID查询二级分类ID
            List<Integer> idList = new LinkedList<>();
            idList.add(goodsSearchQuery.getCategoryId());

            // 查询子分类ID
            List<Integer> childCategoryIds = categoryService.queryIdsByParentId(goodsSearchQuery.getCategoryId());
            if (childCategoryIds != null && !childCategoryIds.isEmpty()) {
                idList.addAll(childCategoryIds);
            }

            // 添加调试日志
            System.out.println("查询分类ID: " + goodsSearchQuery.getCategoryId());
            System.out.println("包含的分类ID列表: " + idList);

            finalCriteria.andIn(Goods::getCategoryId, idList);
        }
        /*if (goodsSearchQuery.getSort() != null) {
            String orderBy = null;
            switch (goodsSearchQuery.getSort()) {
                case "price":
//                    orderBy = "retail_price";
                    if ("desc".equals(goodsSearchQuery.getOrder())) {
                        finalCriteria.sortDesc(Goods::getRetailPrice);
                    } else {
                        finalCriteria.sort(Goods::getRetailPrice);
                    }
                    break;
                default:
                    orderBy = "id";
                    if ("desc".equals(goodsSearchQuery.getOrder())) {
                        finalCriteria.sortDesc(Goods::getId);
                    } else {
                        finalCriteria.sort(Goods::getId);
                    }
            }
        } else {
            //默认按照添加时间排序
        }*/
        finalCriteria.sort(Goods::getSortOrder);
        finalCriteria.fields(
                Goods::getId,
                Goods::getName,
                Goods::getCategoryId,
                Goods::getListPicUrl,
                Goods::getUnitPrice,
                Goods::getGoodsUnit,
                Goods::getRetailPrice);
        finalCriteria.page(goodsSearchQuery.getPageNum(), goodsSearchQuery.getPageSize());
        finalCriteria.page(goodsSearchQuery.getPageNum(), 100);

        List<Goods> goodsList = queryByCriteria(finalCriteria);

        // 添加调试日志
        System.out.println("查询到的商品数量: " + goodsList.size());
        if (!goodsList.isEmpty()) {
            System.out.println("商品分类ID分布:");
            goodsList.stream()
                    .collect(Collectors.groupingBy(Goods::getCategoryId, Collectors.counting()))
                    .forEach((categoryId, count) -> System.out.println("  分类ID " + categoryId + ": " + count + " 个商品"));
        }

        List<Category> categories = categoryService.queryAll();
        GoodsResultVO goodsResultVO = new GoodsResultVO(goodsList, categoryFilter);
        goodsResultVO.setCategoryList(categories);


        return goodsResultVO;
    }

    private List<GoodsDetailVO.GoodsSpecificationVO> queryGoodsDetailSpecificationByGoodsId(Integer goodsId) {
        List<GoodsDetailVO.GoodsSpecificationVO> goodsSpecificationVOList = new LinkedList<>();
        List<GoodsSpecificationDTO> goodsSpecificationBOList = goodsSpecificationService.queryGoodsDetailSpecificationByGoodsId(goodsId);

        goodsSpecificationBOList.stream()
                .collect(Collectors.toMap(GoodsSpecificationDTO::getSpecificationId, g -> g, (g1, g2) -> g2))
                .forEach((k, v) -> {
                    GoodsDetailVO.GoodsSpecificationVO goodsSpecificationVO = new GoodsDetailVO.GoodsSpecificationVO();
                    goodsSpecificationVO.setSpecificationId(k);
                    goodsSpecificationVO.setName(v.getName());
                    goodsSpecificationVO.setValueList(
                            goodsSpecificationBOList.stream()
                                    .filter(g -> g.getSpecificationId().equals(v.getSpecificationId()))
                                    .collect(Collectors.toList())
                    );
                    goodsSpecificationVOList.add(goodsSpecificationVO);
                });

        return goodsSpecificationVOList;
    }


    public GoodsDetailVO queryGoodsDetail(Integer id) {
        User userInfo = JwtHelper.getUser();
        GoodsDetailVO goodsDetailDTO = new GoodsDetailVO();

        Goods goods = ofNullable(queryById(id)).orElseThrow(() -> new WeshopWechatException(CommonResultStatus.RECORD_NOT_EXIST));

        GoodsGallery galleryQuery = new GoodsGallery();
        galleryQuery.setGoodsId(id);
        List<GoodsGallery> goodsGalleryVOList = goodsGalleryService.queryList(galleryQuery);
        List<GoodsAttributeDTO> goodsAttributeVOList = goodsAttributeService.queryGoodsDetailAttributeByGoodsId(id);
        List<GoodsIssue> goodsIssueList = goodsIssueService.queryAll();
        Brand brand = brandService.queryById(goods.getBrandId());

        //商品评价
        int commentCount = commentService.countByCriteria(Criteria.of(Comment.class).andEqualTo(Comment::getValueId, id).andEqualTo(Comment::getTypeId, 0));
        if (commentCount > 0) {
            Comment hotComment = commentService.queryOneByCriteria(Criteria.of(Comment.class).andEqualTo(Comment::getValueId, id).andEqualTo(Comment::getTypeId, 0).page(1, 1));
            GoodsDetailVO.CommentVO.CommentDataVO commentData = new GoodsDetailVO.CommentVO.CommentDataVO();
            String content = new String(Base64.getDecoder().decode(hotComment.getContent()));
            User user = userService.queryById(hotComment.getUserId());
            List<String> picList = commentPictureService.queryList(new CommentPicture().setCommentId(hotComment.getId())).stream()
                    .map(CommentPicture::getPicUrl)
                    .collect(Collectors.toList());

            commentData.setContent(content);
            commentData.setNickname(user.getNickname());
            commentData.setAvatar(user.getAvatar());
            commentData.setPicList(picList);
            commentData.setCreateTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(hotComment.getCreateTime()));
            goodsDetailDTO.setComment(new GoodsDetailVO.CommentVO(commentCount, commentData));
        }

        List<GoodsDetailVO.GoodsSpecificationVO> goodsSpecificationVOList = this.queryGoodsDetailSpecificationByGoodsId(id);
        Product productQuery = new Product();
        productQuery.setGoodsId(id);
        List<Product> productList = productService.queryList(productQuery);

        goodsDetailDTO.setGoods(goods);

        goodsDetailDTO.setBrand(brand);
        goodsDetailDTO.setGoodsGalleryList(goodsGalleryVOList);
        goodsDetailDTO.setGoodsAttributeList(goodsAttributeVOList);
        goodsDetailDTO.setGoodsIssueList(goodsIssueList);
        goodsDetailDTO.setGoodsSpecificationList(goodsSpecificationVOList);
        goodsDetailDTO.setProductList(productList);
        goodsDetailDTO.setUserHasCollect(false);
        if (userInfo != null) {
            //用户是否收藏
            List<Collect> userCollect = collectService.queryByCriteria(Criteria.of(Collect.class).andEqualTo(Collect::getUserId, userInfo.getId()).andEqualTo(Collect::getValueId, id).page(1, 1));

            goodsDetailDTO.setUserHasCollect(userCollect.size() > 0 ? true : false);

            //记录用户足迹 此处使用异步处理
            Footprint footprint = new Footprint().setUserId(userInfo.getId()).setGoodsId(id);
            footprintService.insert(footprint);
        }

        return goodsDetailDTO;
    }

    public List<GoodsListVO> queryRelatedGoods(Integer goodsId) {

        List<RelatedGoods> relatedGoodsList = relatedGoodsService.queryList(new RelatedGoods().setGoodsId(goodsId));
        List<GoodsListVO> goodsList = null;

        if (relatedGoodsList.isEmpty()) {
            //查找同分类下的商品
            Goods goods = queryById(goodsId);

            goodsList = queryByCriteria(Criteria.of(Goods.class).andEqualTo(Goods::getCategoryId, goods.getCategoryId()).page(1, 8)).stream()
                    .map(GoodsListVO::new)
                    .collect(Collectors.toList());
        } else {
            List<Integer> goodsIdList = relatedGoodsList.stream()
                    .map(RelatedGoods::getGoodsId)
                    .collect(Collectors.toList());

            goodsList = queryByCriteria(Criteria.of(Goods.class).andIn(Goods::getId, goodsIdList).page(1, 8)).stream()
                    .map(GoodsListVO::new)
                    .collect(Collectors.toList());
        }
        return goodsList;
    }

    public GoodsCategoryVO queryGoodsCategory(Integer categoryId) {
        if (categoryId == 0) categoryId = 1005000;

        Category currentCategory = ofNullable(categoryService.queryById(categoryId))
                .orElseThrow(() -> new WeshopWechatException(CommonResultStatus.RECORD_NOT_EXIST));
        Category parentCategory = categoryService.queryById(currentCategory.getParentId());

        List<Category> brotherCategory = categoryService.queryList(Category.builder().parentId(currentCategory.getParentId()).build());
        return new GoodsCategoryVO(currentCategory, parentCategory, brotherCategory);
    }

    /**
     * Move product to recycle bin (soft delete)
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "goodsPageInfo", allEntries = true)
    public boolean moveToRecycleBin(Integer id) {
        Goods goods = queryById(id);
        if (goods == null) {
            throw new RuntimeException("商品不存在");
        }

        if (goods.getIsDelete()) {
            throw new RuntimeException("商品已在回收站中");
        }

        goods.setIsDelete(true);
        return updateById(goods) > 0;
    }

    /**
     * Permanently delete product
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "goodsPageInfo", allEntries = true)
    public boolean permanentDelete(Integer id) {
        Goods goods = queryById(id);
        if (goods == null) {
            throw new RuntimeException("商品不存在");
        }

        // Delete related data
        goodsGalleryService.deleteByGoodsId(id);
        goodsSpecificationService.deleteByGoodsId(id);
        productService.deleteByGoodsId(id);
        goodsAttributeService.deleteByGoodsId(id);

        // Delete the goods record
        return deleteById(id) > 0;
    }

    /**
     * Restore product from recycle bin
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "goodsPageInfo", allEntries = true)
    public boolean restoreFromRecycleBin(Integer id) {
        Goods goods = queryById(id);
        if (goods == null) {
            throw new RuntimeException("商品不存在");
        }

        if (!goods.getIsDelete()) {
            throw new RuntimeException("商品不在回收站中");
        }

        goods.setIsDelete(false);
        return updateById(goods) > 0;
    }

    /**
     * Get product type header statistics
     */
    public Map<String, Object> getProductTypeHeader(GoodsParam param) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();

        // 全部商品
        Map<String, Object> all = new HashMap<>();
        all.put("name", "全部");
        all.put("type", 1);
        all.put("count", countByCondition(param, null));
        list.add(all);

        // 出售中
        Map<String, Object> onSale = new HashMap<>();
        onSale.put("name", "出售中");
        onSale.put("type", 2);
        onSale.put("count", countByCondition(param, true));
        list.add(onSale);

        // 仓库中
        Map<String, Object> inStock = new HashMap<>();
        inStock.put("name", "仓库中");
        inStock.put("type", 3);
        inStock.put("count", countByCondition(param, false));
        list.add(inStock);

        // 回收站
        Map<String, Object> recycle = new HashMap<>();
        recycle.put("name", "回收站");
        recycle.put("type", 4);
        recycle.put("count", countDeletedByCondition(param));
        list.add(recycle);

        result.put("list", list);
        return result;
    }

    /**
     * Count products by condition
     */
    private long countByCondition(GoodsParam param, Boolean isOnSale) {
        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", false);

        if (isOnSale != null) {
            queryWrapper.eq("is_on_sale", isOnSale);
        }

        // Add other filter conditions from param
        addFilterConditions(queryWrapper, param);

        return goodsMapper.selectCount(queryWrapper);
    }

    /**
     * Count deleted products by condition
     */
    private long countDeletedByCondition(GoodsParam param) {
        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", true);

        // Add other filter conditions from param
        addFilterConditions(queryWrapper, param);

        return goodsMapper.selectCount(queryWrapper);
    }

    /**
     * Add filter conditions to query wrapper
     */
    private void addFilterConditions(QueryWrapper<Goods> queryWrapper, GoodsParam param) {
        if (param.getCategoryId() != null && param.getCategoryId() > 0) {
            queryWrapper.eq("category_id", param.getCategoryId());
        }

        if (StringUtils.isNotBlank(param.getStoreName())) {
            queryWrapper.like("name", param.getStoreName());
        }

        // Add more conditions as needed based on GoodsParam fields
    }

    /**
     * Convert display systems list to JSON string
     */
    private String convertDisplaySystemsToJson(List<Integer> displaySystems) {
        if (displaySystems == null || displaySystems.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(displaySystems);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert display systems to JSON: {}", displaySystems, e);
            return null;
        }
    }

    /**
     * Convert JSON string to display systems list
     */
    private List<Integer> convertJsonToDisplaySystems(String json) {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, Integer.class));
        } catch (JsonProcessingException e) {
            log.error("Failed to convert JSON to display systems: {}", json, e);
            return new ArrayList<>();
        }
    }

    // ==================== Excel导入相关方法 ====================

    /**
     * 生成商品导入Excel模板
     */
    public String generateGoodsImportTemplate() throws IOException {
        // 创建临时目录
        Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "goods_templates");
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
        }

        // 生成文件名
        String fileName = "商品导入模板_" + System.currentTimeMillis() + ".xlsx";
        String filePath = tempDir.resolve(fileName).toString();

        // 生成模板文件
        GoodsExcelUtils.generateTemplate(filePath);

        return filePath;
    }

    /**
     * 从Excel文件导入商品数据
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "goodsPageInfo", allEntries = true)
    public GoodsImportResultDTO importGoodsFromExcel(MultipartFile file) throws IOException {
        long startTime = System.currentTimeMillis();

        // 保存上传的文件到临时目录
        String tempFilePath = saveUploadedFile(file);

        try {
            // 读取Excel数据
            List<GoodsImportDTO> importList = GoodsExcelUtils.readGoodsFromExcel(tempFilePath);

            if (importList.isEmpty()) {
                return GoodsImportResultDTO.failure("Excel文件中没有有效数据");
            }

            // 验证数据
            validateImportData(importList);

            // 处理导入数据
            List<GoodsImportDTO> successList = new ArrayList<>();
            List<GoodsImportDTO> failureList = new ArrayList<>();

            for (GoodsImportDTO importDTO : importList) {
                if (!importDTO.getIsValid()) {
                    failureList.add(importDTO);
                    continue;
                }

                try {
                    // 转换为GoodsVO并创建商品
                    GoodsVO goodsVO = convertImportDTOToGoodsVO(importDTO);
                    Goods goods = createGoods(goodsVO);

                    importDTO.setProcessStatus(1); // 成功
                    successList.add(importDTO);

                } catch (Exception e) {
                    log.error("导入第{}行商品失败: {}", importDTO.getRowNumber(), e.getMessage(), e);
                    importDTO.setIsValid(false);
                    importDTO.setErrorMessage("创建商品失败: " + e.getMessage());
                    importDTO.setProcessStatus(2); // 失败
                    failureList.add(importDTO);
                }
            }

            long duration = System.currentTimeMillis() - startTime;

            // 构建结果
            GoodsImportResultDTO result = GoodsImportResultDTO.partialSuccess(
                    importList.size(), successList.size(), failureList.size(), successList, failureList);
            result.setDuration(duration);

            return result;

        } finally {
            // 清理临时文件
            try {
                Files.deleteIfExists(Paths.get(tempFilePath));
            } catch (IOException e) {
                log.warn("清理临时文件失败: {}", tempFilePath, e);
            }
        }
    }

    /**
     * 保存上传的文件到临时目录
     */
    private String saveUploadedFile(MultipartFile file) throws IOException {
        // 创建临时目录
        Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "goods_import");
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
        }

        // 生成临时文件名
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename != null && originalFilename.contains(".")
                ? originalFilename.substring(originalFilename.lastIndexOf(".")) : ".xlsx";
        String tempFileName = "import_" + System.currentTimeMillis() + extension;

        Path tempFilePath = tempDir.resolve(tempFileName);

        // 保存文件
        Files.copy(file.getInputStream(), tempFilePath);

        return tempFilePath.toString();
    }

    /**
     * 验证导入数据
     */
    private void validateImportData(List<GoodsImportDTO> importList) {
        for (GoodsImportDTO importDTO : importList) {
            if (!importDTO.getIsValid()) {
                continue; // 跳过已经标记为无效的数据
            }

            List<String> errors = new ArrayList<>();

            // 验证必填字段
            if (StringUtils.isBlank(importDTO.getName())) {
                errors.add("商品名称不能为空");
            }

            if (importDTO.getCategoryId() == null) {
                errors.add("商品分类ID不能为空");
            } else {
                // 验证分类是否存在
                Category category = categoryService.queryById(importDTO.getCategoryId());
                if (category == null) {
                    errors.add("商品分类ID不存在: " + importDTO.getCategoryId());
                } else {
                    importDTO.setCategoryName(category.getName());
                }
            }

            // 根据规格类型验证不同字段
            if (importDTO.getSpecType() == null || importDTO.getSpecType() == 0) {
                // 单规格商品验证
                if (importDTO.getPrice() == null || importDTO.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    errors.add("单规格商品价格必须大于0");
                }
                if (importDTO.getStock() == null || importDTO.getStock() < 0) {
                    errors.add("单规格商品库存数量不能为负数");
                }
            } else {
                // 多规格商品验证
                if (StringUtils.isBlank(importDTO.getSpecItems())) {
                    errors.add("多规格商品必须填写规格定义");
                }
                if (StringUtils.isBlank(importDTO.getSkuAttrs())) {
                    errors.add("多规格商品必须填写SKU列表");
                }

                // 验证JSON格式
                try {
                    if (StringUtils.isNotBlank(importDTO.getSpecItems())) {
                        objectMapper.readTree(importDTO.getSpecItems());
                    }
                    if (StringUtils.isNotBlank(importDTO.getSkuAttrs())) {
                        objectMapper.readTree(importDTO.getSkuAttrs());
                    }
                } catch (JsonProcessingException e) {
                    errors.add("多规格商品的JSON格式不正确: " + e.getMessage());
                }
            }

            // 设置默认值
            if (importDTO.getSpecType() == null) {
                importDTO.setSpecType(0); // 默认单规格
            }
            if (importDTO.getIsOnSale() == null) {
                importDTO.setIsOnSale(1); // 默认上架
            }
            if (importDTO.getIsHot() == null) {
                importDTO.setIsHot(0); // 默认非热门
            }
            if (importDTO.getIsNew() == null) {
                importDTO.setIsNew(0); // 默认非新品
            }
            if (importDTO.getSortOrder() == null) {
                importDTO.setSortOrder(100); // 默认排序
            }
            if (importDTO.getVirtualSales() == null) {
                importDTO.setVirtualSales(0); // 默认虚拟销量
            }

            // 如果有错误，标记为无效
            if (!errors.isEmpty()) {
                importDTO.setIsValid(false);
                importDTO.setErrorMessage(String.join("; ", errors));
                importDTO.setProcessStatus(2); // 失败
            }
        }
    }

    /**
     * 将导入DTO转换为GoodsVO
     */
    private GoodsVO convertImportDTOToGoodsVO(GoodsImportDTO importDTO) {
        GoodsVO goodsVO = new GoodsVO();

        // 基本信息
        goodsVO.setStoreName(importDTO.getName());
        goodsVO.setCategoryIds(Arrays.asList(importDTO.getCategoryId()));
        goodsVO.setStoreInfo(importDTO.getBrief());
        goodsVO.setUnitName(importDTO.getUnit());
        goodsVO.setKeyword(importDTO.getKeywords());
        goodsVO.setDescription(importDTO.getDescription());
        goodsVO.setIsShow(importDTO.getIsOnSale());
        goodsVO.setIsHot(importDTO.getIsHot());
        goodsVO.setIsNew(importDTO.getIsNew());
        goodsVO.setSort(importDTO.getSortOrder());
        goodsVO.setVirtualSales(importDTO.getVirtualSales());
        goodsVO.setUnitPrice(importDTO.getUnitPrice());
        goodsVO.setDetailTag(importDTO.getDetailTag());

        // 图片处理
        if (StringUtils.isNotBlank(importDTO.getImageUrl())) {
            goodsVO.setImage(importDTO.getImageUrl());
        }

        if (StringUtils.isNotBlank(importDTO.getSliderImages())) {
            String[] images = importDTO.getSliderImages().split(",");
            goodsVO.setSliderImages(Arrays.asList(images));
        }

        // 展示系统处理
        if (StringUtils.isNotBlank(importDTO.getDisplaySystems())) {
            try {
                String[] systemIds = importDTO.getDisplaySystems().split(",");
                List<Integer> displaySystems = Arrays.stream(systemIds)
                        .map(String::trim)
                        .map(Integer::valueOf)
                        .collect(Collectors.toList());
                goodsVO.setDisplaySystems(displaySystems);
            } catch (NumberFormatException e) {
                log.warn("解析展示系统ID失败: {}", importDTO.getDisplaySystems());
            }
        }

        // 规格类型
        goodsVO.setSpecType(importDTO.getSpecType());

        if (importDTO.getSpecType() == null || importDTO.getSpecType() == 0) {
            // 单规格商品处理
            Map<String, Object> attr = new HashMap<>();
            attr.put("price", importDTO.getPrice());
            attr.put("cost", importDTO.getCostPrice() != null ? importDTO.getCostPrice() : BigDecimal.ZERO);
            attr.put("ot_price", importDTO.getOtPrice() != null ? importDTO.getOtPrice() : BigDecimal.ZERO);
            attr.put("stock", importDTO.getStock());
            attr.put("bar_code", importDTO.getBarCode() != null ? importDTO.getBarCode() : "");
            attr.put("weight", importDTO.getWeight() != null ? importDTO.getWeight() : BigDecimal.ZERO);
            attr.put("volume", importDTO.getVolume() != null ? importDTO.getVolume() : BigDecimal.ZERO);
            attr.put("pic", importDTO.getSpecImageUrl() != null ? importDTO.getSpecImageUrl() : "");
            attr.put("brokerage", importDTO.getBrokerage() != null ? importDTO.getBrokerage() : BigDecimal.ZERO);
            attr.put("brokerage_two", importDTO.getBrokerageTwo() != null ? importDTO.getBrokerageTwo() : BigDecimal.ZERO);
            attr.put("quota", importDTO.getQuota() != null ? importDTO.getQuota() : 0);
            attr.put("quota_show", importDTO.getQuotaShow() != null && importDTO.getQuotaShow() == 1);

            goodsVO.setAttrs(Arrays.asList(attr));
        } else {
            // 多规格商品处理
            try {
                // 解析规格定义
                if (StringUtils.isNotBlank(importDTO.getSpecItems())) {
                    ObjectMapper mapper = new ObjectMapper();
                    List<Map<String, Object>> items = mapper.readValue(importDTO.getSpecItems(),
                            mapper.getTypeFactory().constructCollectionType(List.class, Map.class));
                    goodsVO.setItems(items);
                }

                // 解析SKU列表
                if (StringUtils.isNotBlank(importDTO.getSkuAttrs())) {
                    ObjectMapper mapper = new ObjectMapper();
                    List<Map<String, Object>> attrs = mapper.readValue(importDTO.getSkuAttrs(),
                            mapper.getTypeFactory().constructCollectionType(List.class, Map.class));
                    goodsVO.setAttrs(attrs);
                }
            } catch (JsonProcessingException e) {
                log.error("解析多规格商品数据失败: {}", e.getMessage());
                throw new RuntimeException("多规格商品数据格式错误: " + e.getMessage());
            }
        }

        // 商品属性处理
        if (StringUtils.isNotBlank(importDTO.getAttributes())) {
            try {
                // 尝试解析JSON格式的属性
                ObjectMapper mapper = new ObjectMapper();
                Map<String, String> attributeMap = mapper.readValue(importDTO.getAttributes(),
                        mapper.getTypeFactory().constructMapType(Map.class, String.class, String.class));

                List<Map<String, String>> paramsList = new ArrayList<>();
                for (Map.Entry<String, String> entry : attributeMap.entrySet()) {
                    Map<String, String> param = new HashMap<>();
                    param.put("attr_name", entry.getKey());
                    param.put("attr_value", entry.getValue());
                    paramsList.add(param);
                }
                goodsVO.setParamsList(paramsList);

            } catch (JsonProcessingException e) {
                log.warn("解析商品属性JSON失败: {}", importDTO.getAttributes());
                // 如果JSON解析失败，可以尝试其他格式或忽略
            }
        }

        return goodsVO;
    }

}
