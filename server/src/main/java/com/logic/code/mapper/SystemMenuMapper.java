package com.logic.code.mapper;

import com.logic.code.entity.system.SystemMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 系统菜单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025/7/7
 */
@Mapper
public interface SystemMenuMapper extends CommonMapper<SystemMenu> {
    
    /**
     * 查询所有显示的菜单，按排序排列
     */
    @Select("SELECT * FROM weshop_system_menu WHERE is_show = 1 ORDER BY sort_order ASC, id ASC")
    List<SystemMenu> selectAllVisible();
    
    /**
     * 根据父级ID查询子菜单
     */
    @Select("SELECT * FROM weshop_system_menu WHERE pid = #{pid} AND is_show = 1 ORDER BY sort_order ASC, id ASC")
    List<SystemMenu> selectByPid(Integer pid);
    
    /**
     * 查询所有根菜单（pid = 0）
     */
    @Select("SELECT * FROM weshop_system_menu WHERE pid = 0 AND is_show = 1 ORDER BY sort_order ASC, id ASC")
    List<SystemMenu> selectRootMenus();
}
