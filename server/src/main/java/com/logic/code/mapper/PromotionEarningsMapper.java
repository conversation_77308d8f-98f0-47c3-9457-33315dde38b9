package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.PromotionEarnings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 推广收益Mapper接口
 */
@Mapper
public interface PromotionEarningsMapper extends BaseMapper<PromotionEarnings> {

    /**
     * 获取收益统计信息（解决聚合查询映射问题）
     * 修复：使用 order_create_time 而不是 create_time，确保按订单实际创建日期统计
     */
    @Select("SELECT " +
            "COUNT(*) as orderCount, " +
            "IFNULL(SUM(commission_amount), 0) as totalCommission, " +
            "IFNULL(SUM(order_amount), 0) as totalOrderAmount " +
            "FROM weshop_promotion_earnings " +
            "WHERE promoter_id = #{promoterId} " +
            "AND status IN ('pending', 'confirmed') " +
            "AND order_create_time >= #{startDate} " +
            "AND order_create_time < #{endDate}")
    Map<String, Object> selectEarningsStats(
            @Param("promoterId") Integer promoterId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate
    );

    /**
     * 获取单一状态的收益总额
     * 修复：使用 order_create_time 而不是 create_time，确保按订单实际创建日期统计
     */
    @Select("SELECT IFNULL(SUM(commission_amount), 0) " +
            "FROM weshop_promotion_earnings " +
            "WHERE promoter_id = #{promoterId} " +
            "AND status = #{status} " +
            "AND order_create_time >= #{startDate} " +
            "AND order_create_time < #{endDate}")
    BigDecimal selectEarningsByStatus(
            @Param("promoterId") Integer promoterId,
            @Param("status") String status,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate
    );

    /**
     * 获取总收益（已确认）
     */
    @Select("SELECT IFNULL(SUM(commission_amount), 0) " +
            "FROM weshop_promotion_earnings " +
            "WHERE promoter_id = #{promoterId} " +
            "AND status = 'confirmed'")
    BigDecimal selectTotalEarnings(@Param("promoterId") Integer promoterId);

    /**
     * 获取每日收益统计（按订单创建日期分组）
     * 修复：使用 order_create_time 而不是 create_time，确保日期与订单数据匹配
     */
    @Select("SELECT " +
            "DATE(order_create_time) as date, " +
            "IFNULL(SUM(commission_amount), 0) as estimatedIncome, " +
            "COUNT(DISTINCT order_id) as orderCount " +
            "FROM weshop_promotion_earnings " +
            "WHERE promoter_id = #{promoterId} " +
            "AND status IN ('pending', 'confirmed') " +
            "AND order_create_time >= #{startDate} " +
            "AND order_create_time < #{endDate} " +
            "GROUP BY DATE(order_create_time) " +
            "ORDER BY DATE(order_create_time)")
    List<Map<String, Object>> selectDailyEarningsStats(
            @Param("promoterId") Integer promoterId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate
    );
}
