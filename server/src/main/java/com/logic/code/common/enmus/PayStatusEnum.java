package com.logic.code.common.enmus;

/**
 * Enum for Payment Status
 */
public enum PayStatusEnum {

    CASH_ON_DELIVERY("到付", 0),
    PENDING_PAYMENT("待付款", 1),
    PAID("已付款", 2),
    PENDING_REFUND("待退款", 3),
    REFUND_SUCCESSFULLY("退款成功", 4),
    REFUND_FAILED("退款失败", 5);

    private String name;
    private int value;

    PayStatusEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    
    public int getValue() {
        return value;
    }
    
    public static PayStatusEnum valueOf(int value) {
        for (PayStatusEnum status : values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant " + PayStatusEnum.class.getSimpleName() + "." + value);
    }
}
