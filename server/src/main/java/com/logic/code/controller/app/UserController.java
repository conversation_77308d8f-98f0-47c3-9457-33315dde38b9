package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.model.vo.UserInfoVO;
import com.logic.code.model.vo.WechatPhoneResultVO;
import com.logic.code.service.MsgNoticeService;
import com.logic.code.service.UserService;
import jakarta.annotation.Resource;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/13 11:15
 * @desc
 */

@RestController
@RequestMapping("/wechat/user")
public class UserController {


    @Resource
    private UserService userService;

    @Resource
    private MsgNoticeService msgNoticeService;

    @RequestMapping("/info")
    public Result<UserInfoVO> getUserInfo() {
        return Result.success(userService.getUserInfo());
    }

    @RequestMapping("/balance")
    public Result<Map<String, Object>> getUserBalance() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> balance = userService.getUserBalance(userInfo.getId());
            return Result.success(balance);
        } catch (Exception e) {
            return Result.failure("获取用户余额失败：" + e.getMessage());
        }
    }




    @RequestMapping("/messageNotice")
    public Result<Boolean> messageNotice(String id) {
        return Result.success(msgNoticeService.insertOrUpdate(id));
    }

    @RequestMapping("/updateNickname")
    public Result<Boolean> updateNickname(String nickname) throws WxErrorException {
        User userInfo = JwtHelper.getUserInfo();
        userInfo.setNickname(nickname);
        userInfo.setUsername(nickname);
        return Result.success(userService.updateById(userInfo) == 1);
    }
    @RequestMapping("/updateAvatar")
    public Result<Boolean> updateAvatar(String avatar) throws WxErrorException {
        User userInfo = JwtHelper.getUserInfo();
        userInfo.setAvatar(avatar);
        return Result.success(userService.updateById(userInfo) == 1);
    }

    @RequestMapping("/generatePromotionQrCode")
    public Result<Map<String, String>> generatePromotionQrCode() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, String> qrCodeInfo = userService.generatePromotionQrCode(userInfo.getId());
            return Result.success(qrCodeInfo);
        } catch (Exception e) {
            return Result.failure("生成推广二维码失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getPromotionStats")
    public Result<Map<String, Object>> getPromotionStats() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> stats = userService.getPromotionStats(userInfo.getId());
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取推广统计失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getPromotionUserDetail")
    public Result<Map<String, Object>> getPromotionUserDetail(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Integer userId = Integer.parseInt(params.get("userId").toString());
            
            Map<String, Object> userDetail = userService.getPromotionUserDetail(userInfo.getId(), userId);
            return Result.success(userDetail);
        } catch (Exception e) {
            return Result.failure("获取推广用户详情失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getEarningsStats")
    public Result<Map<String, Object>> getEarningsStats() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> stats = userService.getEarningsStats(userInfo.getId());
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取收益统计失败：" + e.getMessage());
        }
    }

    @RequestMapping("/submitWithdraw")
    public Result<String> submitWithdraw(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            String amount = params.get("amount").toString();
            String paymentMethod = params.get("paymentMethod").toString();

            boolean success = userService.submitWithdraw(userInfo.getId(), amount, paymentMethod);
            if (success) {
                return Result.success("提现申请已提交");
            } else {
                return Result.failure("提现申请失败");
            }
        } catch (Exception e) {
            return Result.failure("提现申请失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getDailyEarningsStats")
    public Result<Map<String, Object>> getDailyEarningsStats() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> stats = userService.getDailyEarningsStats(userInfo.getId());
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取每日收益统计失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getMonthlyEarningsStats")
    public Result<Map<String, Object>> getMonthlyEarningsStats() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> stats = userService.getMonthlyEarningsStats(userInfo.getId());
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取月度收益统计失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getDayOrderDetail")
    public Result<Map<String, Object>> getDayOrderDetail(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            String date = params.get("date").toString();
            Map<String, Object> detail = userService.getDayOrderDetail(userInfo.getId(), date);
            return Result.success(detail);
        } catch (Exception e) {
            return Result.failure("获取每日订单明细失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getMonthOrderDetail")
    public Result<Map<String, Object>> getMonthOrderDetail(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            String month = params.get("month").toString();
            Map<String, Object> detail = userService.getMonthOrderDetail(userInfo.getId(), month);
            return Result.success(detail);
        } catch (Exception e) {
            return Result.failure("获取月度订单明细失败：" + e.getMessage());
        }
    }

    /**
     * 建立推广关系（用于已登录用户扫码推广二维码的情况）
     * @param params 包含推广者ID的参数
     * @return 建立结果
     */
    @RequestMapping("/establishPromotionRelation")
    public Result<String> establishPromotionRelation(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            String promotionScene = params.get("promotionScene").toString();

            boolean success = userService.establishPromotionRelationWithLevel(userInfo.getId(), promotionScene);
            if (success) {
                return Result.success("推广关系建立成功");
            } else {
                return Result.failure("推广关系建立失败");
            }
        } catch (Exception e) {
            return Result.failure("建立推广关系失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户推广等级信息
     * @return 推广等级信息
     */
    @RequestMapping("/getPromotionLevelInfo")
    public Result<Map<String, Object>> getPromotionLevelInfo() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> levelInfo = userService.getUserPromotionLevelInfo(userInfo.getId());
            return Result.success(levelInfo);
        } catch (Exception e) {
            return Result.failure("获取推广等级信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取推广积分记录
     * @param params 包含分页参数的Map
     * @return 推广积分记录
     */
    @RequestMapping("/getPromotionPointsRecords")
    public Result<Map<String, Object>> getPromotionPointsRecords(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Integer page = params.get("page") != null ? Integer.parseInt(params.get("page").toString()) : 1;
            Integer size = params.get("size") != null ? Integer.parseInt(params.get("size").toString()) : 20;
            
            Map<String, Object> records = userService.getPromotionPointsRecords(userInfo.getId(), page, size);
            return Result.success(records);
        } catch (Exception e) {
            return Result.failure("获取推广积分记录失败：" + e.getMessage());
        }
    }

    /**
     * 性能测试接口 - 测试getPromotionStats优化效果
     * 仅用于开发测试，生产环境应移除
     */
    @RequestMapping("/performanceTest")
    public Result<Map<String, Object>> performanceTest() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> testResult = userService.performanceTest(userInfo.getId());
            return Result.success(testResult);
        } catch (Exception e) {
            return Result.failure("性能测试失败：" + e.getMessage());
        }
    }


}
