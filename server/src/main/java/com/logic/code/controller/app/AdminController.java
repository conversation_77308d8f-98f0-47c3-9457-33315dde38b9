package com.logic.code.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.model.query.OrderQuery;
import com.logic.code.model.vo.OrderDetailVO;
import com.logic.code.model.vo.OrderListVO;
import com.logic.code.service.OrderService;
import com.logic.code.service.OrderGoodsService;
import com.logic.code.service.UserService;
import com.logic.code.entity.order.OrderGoods;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 管理员相关接口
 * <AUTHOR>
 * @date 2025/1/26
 */
@RestController
@RequestMapping("/wechat/admin")
public class AdminController {

    @Resource
    private UserService userService;

    @Resource
    private OrderService orderService;

    @Resource
    private OrderGoodsService orderGoodsService;

    /**
     * 检查管理员权限
     */
    private boolean checkAdminPermission() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            return userInfo != null && userInfo.getUserLevelId() != null && userInfo.getUserLevelId() == 1;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取管理员统计数据
     * @return 统计数据
     */
    @RequestMapping("/stats")
    public Result<Map<String, Object>> getAdminStats() {
        if (!checkAdminPermission()) {
            return Result.failure("权限不足");
        }

        try {
            Map<String, Object> stats = new HashMap<>();

            // 获取用户统计
            long totalUsers = userService.count();
            stats.put("totalUsers", totalUsers);

            // 获取订单统计
            long totalOrders = orderService.count();
            stats.put("totalOrders", totalOrders);

            // 获取今日新增用户数
            LocalDate today = LocalDate.now();
            Date todayStart = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date todayEnd = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

            QueryWrapper<User> todayUserWrapper = new QueryWrapper<>();
            todayUserWrapper.between("register_time", todayStart, todayEnd);
            long todayNewUsers = userService.count(todayUserWrapper);
            stats.put("todayNewUsers", todayNewUsers);

            // 获取今日订单数
            QueryWrapper<Order> todayOrderWrapper = new QueryWrapper<>();
            todayOrderWrapper.between("create_time", todayStart, todayEnd);
            long todayOrders = orderService.count(todayOrderWrapper);
            stats.put("todayOrders", todayOrders);

            // 获取本月订单数
            LocalDate monthStart = today.withDayOfMonth(1);
            Date monthStartDate = Date.from(monthStart.atStartOfDay(ZoneId.systemDefault()).toInstant());

            QueryWrapper<Order> monthOrderWrapper = new QueryWrapper<>();
            monthOrderWrapper.between("create_time", monthStartDate, todayEnd);
            long monthOrders = orderService.count(monthOrderWrapper);
            stats.put("monthOrders", monthOrders);

            // 获取总交易额（已支付订单）
            QueryWrapper<Order> paidOrderWrapper = new QueryWrapper<>();
            paidOrderWrapper.eq("pay_status", 1); // 假设1表示已支付
            paidOrderWrapper.select("IFNULL(SUM(actual_price), 0) as total_amount");
            List<Map<String, Object>> totalAmountResult = orderService.selectMaps(paidOrderWrapper);
            BigDecimal totalAmount = BigDecimal.ZERO;
            if (!totalAmountResult.isEmpty() && totalAmountResult.get(0).get("total_amount") != null) {
                totalAmount = new BigDecimal(totalAmountResult.get(0).get("total_amount").toString());
            }
            stats.put("totalAmount", totalAmount.toString());

            // 获取今日交易额
            QueryWrapper<Order> todayAmountWrapper = new QueryWrapper<>();
            todayAmountWrapper.eq("pay_status", 1);
            todayAmountWrapper.between("create_time", todayStart, todayEnd);
            todayAmountWrapper.select("IFNULL(SUM(actual_price), 0) as today_amount");
            List<Map<String, Object>> todayAmountResult = orderService.selectMaps(todayAmountWrapper);
            BigDecimal todayAmount = BigDecimal.ZERO;
            if (!todayAmountResult.isEmpty() && todayAmountResult.get(0).get("today_amount") != null) {
                todayAmount = new BigDecimal(todayAmountResult.get(0).get("today_amount").toString());
            }
            stats.put("todayAmount", todayAmount.toString());

            // 获取本月交易额
            QueryWrapper<Order> monthAmountWrapper = new QueryWrapper<>();
            monthAmountWrapper.eq("pay_status", 1);
            monthAmountWrapper.between("create_time", monthStartDate, todayEnd);
            monthAmountWrapper.select("IFNULL(SUM(actual_price), 0) as month_amount");
            List<Map<String, Object>> monthAmountResult = orderService.selectMaps(monthAmountWrapper);
            BigDecimal monthAmount = BigDecimal.ZERO;
            if (!monthAmountResult.isEmpty() && monthAmountResult.get(0).get("month_amount") != null) {
                monthAmount = new BigDecimal(monthAmountResult.get(0).get("month_amount").toString());
            }
            stats.put("monthAmount", monthAmount.toString());

            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取管理员订单列表
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param orderStatus 订单状态
     * @param keyword 搜索关键词
     * @return 订单列表
     */
    @RequestMapping("/order/list")
    public Result<Map<String, Object>> getAdminOrderList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Integer orderStatus,
            @RequestParam(required = false) String keyword) {

        if (!checkAdminPermission()) {
            return Result.failure("权限不足");
        }

        try {
            // 构建查询条件
            QueryWrapper<Order> wrapper = new QueryWrapper<>();

            // 按状态筛选
            if (orderStatus != null) {
                wrapper.eq("order_status", orderStatus);
            }

            // 按关键词搜索（订单号或用户信息）
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.and(w -> w.like("order_sn", keyword)
                    .or().exists("SELECT 1 FROM weshop_user u WHERE u.id = weshop_order.user_id AND (u.nickname LIKE {0} OR u.username LIKE {0})", "%" + keyword + "%"));
            }

            // 按创建时间倒序
            wrapper.orderByDesc("create_time");

            // 分页查询
            Page<Order> page = new Page<>(pageNum, pageSize);
            Page<Order> orderPage = orderService.selectPage(page, wrapper);

            // 转换为VO
            List<Map<String, Object>> orderList = new ArrayList<>();
            for (Order order : orderPage.getRecords()) {
                Map<String, Object> orderMap = new HashMap<>();
                orderMap.put("id", order.getId());
                orderMap.put("orderSn", order.getOrderSn());
                orderMap.put("userId", order.getUserId());
                orderMap.put("orderStatus", order.getOrderStatus().getValue());
                orderMap.put("orderStatusText", order.getOrderStatus().getName());
                orderMap.put("orderPrice", order.getOrderPrice());
                orderMap.put("actualPrice", order.getActualPrice());
                orderMap.put("createTime", order.getCreateTime());
                orderMap.put("payTime", order.getPayTime());
                orderMap.put("consignee", order.getConsignee());
                orderMap.put("mobile", order.getMobile());
                orderMap.put("address", order.getAddress());

                // 获取用户信息
                User user = userService.queryById(order.getUserId());
                if (user != null) {
                    orderMap.put("userName", user.getNickname() != null ? user.getNickname() : user.getUsername());
                    orderMap.put("userAvatar", user.getAvatar());
                    orderMap.put("userMobile", user.getMobile());
                }

                // 获取订单商品信息
                List<OrderGoods> orderGoodsList = orderGoodsService.queryList(
                    OrderGoods.builder().orderId(order.getId()).build()
                );
                List<Map<String, Object>> goodsList = new ArrayList<>();
                for (OrderGoods orderGoods : orderGoodsList) {
                    Map<String, Object> goodsMap = new HashMap<>();
                    goodsMap.put("id", orderGoods.getId());
                    goodsMap.put("goodsName", orderGoods.getGoodsName());
                    goodsMap.put("picUrl", orderGoods.getListPicUrl());
                    goodsMap.put("price", orderGoods.getRetailPrice());
                    goodsMap.put("number", orderGoods.getNumber());
                    goodsMap.put("specifications", orderGoods.getGoodsSpecificationNameValue());
                    goodsList.add(goodsMap);
                }
                orderMap.put("goodsList", goodsList);

                orderList.add(orderMap);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("list", orderList);
            result.put("total", orderPage.getTotal());
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("pages", orderPage.getPages());

            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取订单列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取管理员用户列表
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param type 用户类型
     * @param keyword 搜索关键词
     * @return 用户列表
     */
    @RequestMapping("/user/list")
    public Result<Map<String, Object>> getAdminUserList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String keyword) {

        if (!checkAdminPermission()) {
            return Result.failure("权限不足");
        }

        try {
            // 构建查询条件
            QueryWrapper<User> wrapper = new QueryWrapper<>();

            // 按类型筛选
            if ("new".equals(type)) {
                // 最近7天注册的用户
                LocalDate sevenDaysAgo = LocalDate.now().minusDays(7);
                Date sevenDaysAgoDate = Date.from(sevenDaysAgo.atStartOfDay(ZoneId.systemDefault()).toInstant());
                wrapper.ge("register_time", sevenDaysAgoDate);
            } else if ("vip".equals(type)) {
                // VIP用户
                wrapper.eq("user_level_id", 1);
            } else if ("active".equals(type)) {
                // 活跃用户（最近30天有登录）
                LocalDate thirtyDaysAgo = LocalDate.now().minusDays(30);
                Date thirtyDaysAgoDate = Date.from(thirtyDaysAgo.atStartOfDay(ZoneId.systemDefault()).toInstant());
                wrapper.ge("last_login_time", thirtyDaysAgoDate);
            }

            // 按关键词搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.and(w -> w.like("nickname", keyword)
                    .or().like("username", keyword)
                    .or().like("mobile", keyword));
            }

            // 按注册时间倒序
            wrapper.orderByDesc("register_time");

            // 分页查询
            Page<User> page = new Page<>(pageNum, pageSize);
            Page<User> userPage = userService.selectPage(page, wrapper);

            // 转换为VO
            List<Map<String, Object>> userList = new ArrayList<>();
            for (User user : userPage.getRecords()) {
                Map<String, Object> userMap = new HashMap<>();
                userMap.put("id", user.getId());
                userMap.put("nickname", user.getNickname());
                userMap.put("username", user.getUsername());
                userMap.put("avatar", user.getAvatar());
                userMap.put("mobile", user.getMobile());
                userMap.put("userLevelId", user.getUserLevelId());
                userMap.put("userLevelText", user.getUserLevelId() == 1 ? "VIP" : "L" + (user.getUserLevelId() != null ? user.getUserLevelId() : 0));
                userMap.put("registerTime", user.getRegisterTime());
                userMap.put("lastLoginTime", user.getLastLoginTime());
                userMap.put("promoterId", user.getPromoterId());
                userMap.put("promotionCount", user.getPromotionCount() != null ? user.getPromotionCount() : 0);

                // 获取用户订单统计
                QueryWrapper<Order> orderWrapper = new QueryWrapper<>();
                orderWrapper.eq("user_id", user.getId());
                long orderCount = orderService.count(orderWrapper);
                userMap.put("orderCount", orderCount);

                // 获取用户消费总额
                QueryWrapper<Order> amountWrapper = new QueryWrapper<>();
                amountWrapper.eq("user_id", user.getId());
                amountWrapper.eq("pay_status", 1);
                amountWrapper.select("IFNULL(SUM(actual_price), 0) as total_amount");
                List<Map<String, Object>> amountResult = orderService.selectMaps(amountWrapper);
                BigDecimal totalAmount = BigDecimal.ZERO;
                if (!amountResult.isEmpty() && amountResult.get(0).get("total_amount") != null) {
                    totalAmount = new BigDecimal(amountResult.get(0).get("total_amount").toString());
                }
                userMap.put("totalAmount", totalAmount.toString());

                // 模拟其他数据
                userMap.put("points", 0);
                userMap.put("balance", "0.00");
                userMap.put("isOnline", Math.random() > 0.5);

                // 获取推广者信息
                if (user.getPromoterId() != null) {
                    User promoter = userService.queryById(user.getPromoterId());
                    if (promoter != null) {
                        userMap.put("promoterName", promoter.getNickname() != null ? promoter.getNickname() : promoter.getUsername());
                    }
                }

                userList.add(userMap);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("list", userList);
            result.put("total", userPage.getTotal());
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("pages", userPage.getPages());

            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 管理员发货操作
     * @param orderId 订单ID
     * @return 操作结果
     */
    @RequestMapping("/order/deliver")
    public Result<String> deliverOrder(@RequestParam Integer orderId) {
        if (!checkAdminPermission()) {
            return Result.failure("权限不足");
        }

        try {
            Order order = orderService.queryById(orderId);
            if (order == null) {
                return Result.failure("订单不存在");
            }

            if (order.getOrderStatus().getValue() != 1) {
                return Result.failure("订单状态不允许发货");
            }

            // 更新订单状态为已发货
            order.setOrderStatus(com.logic.code.common.enmus.OrderStatusEnum.WAIT_RECEIVE);
            order.setShippingStatus((short) 1);
            orderService.updateById(order);

            return Result.success("发货成功");
        } catch (Exception e) {
            return Result.failure("发货失败：" + e.getMessage());
        }
    }

    /**
     * 管理员取消订单操作
     * @param orderId 订单ID
     * @return 操作结果
     */
    @RequestMapping("/order/cancel")
    public Result<String> cancelOrder(@RequestParam Integer orderId) {
        if (!checkAdminPermission()) {
            return Result.failure("权限不足");
        }

        try {
            Order order = orderService.queryById(orderId);
            if (order == null) {
                return Result.failure("订单不存在");
            }

            if (order.getOrderStatus().getValue() != 0) {
                return Result.failure("只能取消待支付订单");
            }

            // 更新订单状态为已取消
            order.setOrderStatus(com.logic.code.common.enmus.OrderStatusEnum.CANCELLED);
            orderService.updateById(order);

            return Result.success("订单已取消");
        } catch (Exception e) {
            return Result.failure("取消订单失败：" + e.getMessage());
        }
    }
}
