package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.entity.order.OrderExpress;
import com.logic.code.model.query.OrderQuery;
import com.logic.code.model.vo.OrderDetailVO;
import com.logic.code.service.OrderExpressService;
import com.logic.code.service.OrderService;
import com.logic.code.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/9 11:47
 * @desc 订单管理控制器
 */

@RestController
@RequestMapping("/adminapi/order")
@Slf4j
public class OrderController {

    @Resource
    OrderService orderService;

    @Resource
    UserService userService;

    @Resource
    OrderExpressService orderExpressService;

    /**
     * 获取订单列表
     * @param orderQuery 查询条件
     * @return 订单列表
     */
    @RequestMapping("/list")
    public Result<Page> queryOrderList(OrderQuery orderQuery) {
        Page listVOS = orderService.getOrderList(orderQuery);
        return Result.success(listVOS);
    }

    /**
     * 获取订单详情
     * @param id 订单ID
     * @return 订单详情
     */
    @RequestMapping("/info/{id}")
    public Result<OrderDetailVO> getOrderDetail(@PathVariable("id") Integer id) {
        OrderDetailVO orderDetail = orderService.queryOrderDetail(id);
        return Result.success(orderDetail);
    }

    /**
     * 获取物流信息
     * @param id 订单ID
     * @return 物流信息
     */
    @RequestMapping("/express/{id}")
    public Result<String> getExpressInfo(@PathVariable("id") Integer id) {
        // 记录日志
        id = 148;
        System.out.println("Fetching logistics data for order ID: " + id);

        // 查询订单物流信息
        OrderExpress orderExpress = orderExpressService.queryByOrderId(id);

        if (orderExpress != null) {
            // 从OrderExpress中获取traces物流轨迹数据
            String tracesJson = orderExpress.getTraces();
            return Result.success(tracesJson);
        }


        return Result.failure("未获取到物流数据");
    }

    /**
     * 管理员发货
     */
    @PostMapping("/deliver")
    public Result<String> deliver(@RequestBody Map<String, Object> deliveryInfo) {
        // 检查管理员权限
        User userInfo = JwtHelper.getUserInfo();
        if (userInfo == null || userInfo.getUserLevelId() == null || !userInfo.getUserLevelId().equals(1)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.UNAUTHORIZED);
        }

        try {
            Integer orderId = (Integer) deliveryInfo.get("orderId");
            String expressType = (String) deliveryInfo.get("expressType");
            String expressCode = (String) deliveryInfo.get("expressCode");
            String trackingNumber = (String) deliveryInfo.get("trackingNumber");

            if (orderId == null || expressType == null || expressCode == null || trackingNumber == null) {
                throw new WeshopWechatException(WeshopWechatResultStatus.PARAM_ERROR);
            }

            // 调用订单服务的发货方法
            boolean success = orderService.deliverOrder(orderId, expressType, expressCode, trackingNumber);
            if (success) {
                log.info("管理员{}发货订单{}成功，快递公司：{}，快递单号：{}", 
                    userInfo.getId(), orderId, expressType, trackingNumber);
                return Result.success("发货成功");
            } else {
                return Result.failure("发货失败");
            }
        } catch (Exception e) {
            log.error("管理员发货失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.SYSTEM_ERROR);
        }
    }
}
