package com.logic.code.controller.app;

import com.logic.code.common.exception.WeshopException;
import com.logic.code.common.response.Result;
import com.logic.code.entity.goods.Goods;
import com.logic.code.entity.goods.Product;
import com.logic.code.model.query.GoodsSearchQuery;
import com.logic.code.model.vo.*;
import com.logic.code.service.GoodsService;
import com.logic.code.service.MemberDayTipService;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat/goods")
@Validated
public class WechatGoodsController {

    @Autowired
    private GoodsService goodsService;
    
    @Autowired
    private MemberDayTipService memberDayTipService;


    @GetMapping("/count")
    public Result count() {
        return Result.success(goodsService.count(Goods.builder().isDelete(false).isOnSale(true).build()));
    }

    @GetMapping("/related")
    public Result<List<GoodsListVO>> queryRelatedGoods(@NotNull @RequestParam("id") Integer id) {
        return Result.success(goodsService.queryRelatedGoods(id));
    }

    /**
     * 新品首发
     *
     * @return
     */
    @GetMapping("/new")
    public Result<BannerInfoVO> newGoods() {
        BannerInfoVO bannerInfo = new BannerInfoVO();
        bannerInfo.setName("坚持初心，为你寻觅世间好物");
        bannerInfo.setImgUrl("http://yanxuan.nosdn.127.net/8976116db321744084774643a933c5ce.png");
        return Result.success(bannerInfo);
    }

    /**
     * 人气推荐
     *
     * @return
     */
    @GetMapping("/hot")
    public Result<BannerInfoVO> hotGoods() {
        BannerInfoVO bannerInfo = new BannerInfoVO();
        bannerInfo.setName("大家都在买的严选好物");
        bannerInfo.setImgUrl("http://yanxuan.nosdn.127.net/8976116db321744084774643a933c5ce.png");
        return Result.success(bannerInfo);
    }

    @GetMapping("/category")
    public Result<GoodsCategoryVO> queryGoodsCategory(@RequestParam("categoryId") @NotNull Integer categoryId) {
        return Result.success(goodsService.queryGoodsCategory(categoryId));
    }

    @GetMapping("/list")
    /*@Cacheable(value = "goodsPageInfo",
               keyGenerator = "goodsCacheKeyGenerator",
               condition = "#goodsSearchQuery.cacheable",
               unless = "#result == null || #result.data == null || #result.data.goodsList == null || #result.data.goodsList.size() == 0")*/
    public Result<GoodsResultVO> queryGoodsPageInfo(GoodsSearchQuery goodsSearchQuery) {
        // 设置默认分页参数
        if (goodsSearchQuery.getPageSize() <= 0) {
            goodsSearchQuery.setPageSize(20);
        }
        if (goodsSearchQuery.getPageNum() <= 0) {
            goodsSearchQuery.setPageNum(1);
        }

        return Result.success(goodsService.queryList(goodsSearchQuery));
    }

    @GetMapping("/detail")
    public Result<GoodsDetailVO> queryGoodsDetail(@RequestParam("id") @NotNull Integer id) {
        return Result.success(goodsService.queryGoodsDetail(id));
    }

    /**
     * 验证规格组合并获取产品信息
     */
    @GetMapping("/validateSpec")
    public Result<Map<String, Object>> validateSpecification(
            @RequestParam("goodsId") @NotNull Integer goodsId,
            @RequestParam("specIds") String specIds) {

        Map<String, Object> result = new HashMap<>();

        // 查找匹配的产品
        Product product = goodsService.findProductBySpecification(goodsId, specIds);

        if (product != null) {
            result.put("valid", true);
            result.put("productId", product.getId());
            result.put("price", product.getRetailPrice());
            result.put("stock", product.getGoodsNumber());
            result.put("canAddToCart", product.getGoodsNumber() > 0);
        } else {
            result.put("valid", false);
            result.put("productId", null);
            result.put("price", null);
            result.put("stock", 0);
            result.put("canAddToCart", false);
        }

        return Result.success(result);
    }

    /**
     * 获取会员日提示信息
     * @param position 显示位置 (goods-商品页, index-首页)
     * @return 会员日提示信息
     */
    @GetMapping("/memberDayTip")
    public Result<MemberDayTipVO> getMemberDayTip(@RequestParam(value = "position", defaultValue = "goods") String position) {
        return Result.success(memberDayTipService.getActiveByPosition(position));
    }

}
