package com.logic.code.controller.admin;

import java.io.File;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.google.common.io.Files;
import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JsonUtils;
import com.logic.code.entity.system.SystemMenu;
import com.logic.code.service.SystemMenuService;
import jakarta.annotation.Resource;
import logic.orm.utils.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2025/6/8 11:45
 * @desc
 */

@Slf4j
@RestController
@RequestMapping("/adminapi")
public class LoginController {

    @Resource
    private SystemMenuService systemMenuService;


    @RequestMapping("/login")
    public Result<JSONObject> login() {
        return Result.success(JSONObject.parseObject(getJson("login.json")));
    }

    private static String getJson(String fileName) {
        try {
            StringBuffer json = new StringBuffer();
            List<String> lines = Files.readLines(new File("E:\\project\\code\\myself\\wjsy_shop\\server\\src\\main\\resources\\json\\" + fileName), Charset.defaultCharset());
            if (ListUtils.isNotBlank(lines)) {
                for (String line : lines) {
                    json.append(line);
                }
            }
            return json.toString();
        } catch (IOException e) {
            return null;
        }
    }
    @RequestMapping("/login2")
    public Result<Map<String, Object>> login1() {
        // 构建登录响应数据
        Map<String, Object> response = new HashMap<>();

        // 设置token和过期时间（这里使用固定值，实际项目中应该动态生成）
        response.put("token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************.qSZMKoFq8FB64cpc0hnrGz1lUt7AKbEMUyyCJuYwE58");
        response.put("expires_time", **********);

        // 从数据库获取菜单数据
        List<SystemMenu> menuTree = systemMenuService.getMenuTree();
        response.put("menus", menuTree);
        log.info("登录成功，返回数据：{}", response);
        // 添加用户信息
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", 5);
        userInfo.put("account", "demo");
        userInfo.put("head_pic", "https://v5.crme222b.net/uploads/attach/2023/09/********/f41769bef07f62a2d3d5e876aba2eb4f.png");
        userInfo.put("level", 1);
        userInfo.put("real_name", "demo");
        response.put("user_info", userInfo);

        // 添加系统配置信息
        response.put("logo", "https://v5.crmeb.net/statics/system_images/admin_logo_big.png");
        response.put("logo_square", "https://v5.crmeb.net/statics/system_images/admin_logo_small.png");
        response.put("version", "CRMEB-BZ v5.6.1");
        response.put("newOrderAudioLink", "");
        response.put("queue", true);
        response.put("timer", false);
        response.put("site_name", "CRMEB标准版");

        // 添加站点功能配置（数组格式）
        // 使用List确保正确的JSON数组格式
        List<String> siteFuncList = Arrays.asList("seckill", "bargain", "combination");
        response.put("site_func", siteFuncList);

        // 添加用户权限数组（从原始JSON数据中提取的完整权限列表）
        // 由于权限列表很长，这里只包含核心权限，实际项目中应该从数据库动态获取
        List<String> uniqueAuthList = getUniqueAuthList();
        response.put("unique_auth", uniqueAuthList);

        return Result.success(response);
    }

    /**
     * 获取用户权限列表
     * 从login.json文件中读取unique_auth数组
     *
     * @return 权限列表
     */
    private List<String> getUniqueAuthList() {
        try {
            // 读取login.json文件
            ClassPathResource resource = new ClassPathResource("json/login.json");
            String jsonContent = new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);

            // 解析JSON
            Map<String, Object> loginData = JsonUtils.toObject(jsonContent, Map.class);

            if (loginData != null && loginData.containsKey("unique_auth")) {
                Object uniqueAuthObj = loginData.get("unique_auth");
                if (uniqueAuthObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> uniqueAuthList = (List<String>) uniqueAuthObj;
                    return uniqueAuthList;
                }
            }

            log.warn("无法从login.json中获取unique_auth数据，返回空列表");
            return new ArrayList<>();

        } catch (IOException e) {
            log.error("读取login.json文件失败", e);
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("解析login.json文件失败", e);
            return new ArrayList<>();
        }
    }
}
