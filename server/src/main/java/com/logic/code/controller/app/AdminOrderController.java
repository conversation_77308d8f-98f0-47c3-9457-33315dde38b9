package com.logic.code.controller.app;

import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.model.query.AdminOrderQuery;
import com.logic.code.model.vo.AdminOrderListVO;
import com.logic.code.model.vo.OrderListVO;
import com.logic.code.service.OrderGoodsService;
import com.logic.code.service.OrderService;
import com.logic.code.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 管理员订单控制器
 * <AUTHOR>
 * @date 2025/1/27
 */
@RestController
@RequestMapping("/wechat/admin/order")
@Slf4j
public class AdminOrderController {

    @Resource
    private OrderService orderService;

    @Resource
    private OrderGoodsService orderGoodsService;

    @Resource
    private UserService userService;

    /**
     * 管理员订单列表
     */
    @GetMapping("/list")
    public Object list(AdminOrderQuery query) {
        // 检查管理员权限
        User userInfo = JwtHelper.getUserInfo();
 /*       if (userInfo == null || userInfo.getUserLevelId() == null || !userInfo.getUserLevelId().equals(1)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.UNAUTHORIZED);
        }*/

        try {
            // 获取所有订单
            List<Order> allOrders = orderService.queryAll();

            // 过滤和搜索
            List<Order> filteredOrders = allOrders.stream()
                .filter(order -> {
                    // 状态过滤
                    if (query.getOrderStatus() != null && !query.getOrderStatus().isEmpty()) {
                        return (order.getOrderStatus().getValue()+"").equals(query.getOrderStatus());
                    }
                    return true;
                })
                .filter(order -> {
                    // 关键词搜索：订单号、用户昵称、商品名称
                    if (query.getKeyword() != null && !query.getKeyword().trim().isEmpty()) {
                        String keyword = query.getKeyword().toLowerCase().trim();

                        // 搜索订单号
                        if (order.getOrderSn().toLowerCase().contains(keyword)) {
                            return true;
                        }

                        // 搜索用户昵称
                        User orderUser = userService.queryById(order.getUserId());
                        if (orderUser != null && orderUser.getNickname() != null &&
                            orderUser.getNickname().toLowerCase().contains(keyword)) {
                            return true;
                        }

                        // 搜索商品名称
                        List<OrderGoods> orderGoodsList = orderGoodsService.queryList(
                            OrderGoods.builder().orderId(order.getId()).build()
                        );
                        boolean hasMatchingGoods = orderGoodsList.stream()
                            .anyMatch(goods -> goods.getGoodsName() != null &&
                                     goods.getGoodsName().toLowerCase().contains(keyword));

                        return hasMatchingGoods;
                    }
                    return true;
                })
                .collect(Collectors.toList());

            // 排序处理
            filteredOrders = applySorting(filteredOrders, query.getSortField(), query.getSortOrder());

            // 分页处理
            int pageNum = query.getPageNum() != null ? query.getPageNum() : 1;
            int pageSize = query.getPageSize() != null ? query.getPageSize() : 10;
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, filteredOrders.size());

            List<Order> pagedOrders = filteredOrders.subList(startIndex, endIndex);

            // 构建返回数据
            List<AdminOrderListVO> orderVOList = new ArrayList<>();
            for (Order order : pagedOrders) {
                User orderUser = userService.queryById(order.getUserId());
                List<OrderGoods> orderGoodsList = orderGoodsService.queryList(
                    OrderGoods.builder().orderId(order.getId()).build()
                );

                AdminOrderListVO orderVO = new AdminOrderListVO();
                orderVO.setId(order.getId());
                orderVO.setOrderSn(order.getOrderSn());
                orderVO.setOrderStatus(order.getOrderStatus().getValue());
                orderVO.setOrderStatusText(order.getOrderStatus().getName());
                orderVO.setOrderPrice(order.getOrderPrice());
                orderVO.setCreateTime(order.getCreateTime().toString());
                orderVO.setUserName(orderUser != null ? orderUser.getNickname() : "未知用户");
                orderVO.setUserAvatar(orderUser != null ? orderUser.getAvatar() : null);
                orderVO.setUserMobile(orderUser != null ? orderUser.getMobile() : null);
                orderVO.setGoodsList(orderGoodsList);

                orderVOList.add(orderVO);
            }

            // 构建分页信息
            AdminOrderQuery.PageResult result = new AdminOrderQuery.PageResult();
            result.setList(orderVOList);
            result.setPageNum(pageNum);
            result.setPageSize(pageSize);
            result.setTotal(filteredOrders.size());
            result.setPages((int) Math.ceil((double) filteredOrders.size() / pageSize));

            return Result.success(result);

        } catch (Exception e) {
            log.error("获取管理员订单列表失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.SYSTEM_ERROR);
        }
    }


    /**
     * 管理员取消订单
     */
    @PostMapping("/cancel")
    public Object cancel(@RequestParam Integer orderId) {
        // 检查管理员权限
        User userInfo = JwtHelper.getUserInfo();
        if (userInfo == null || userInfo.getUserLevelId() == null || !userInfo.getUserLevelId().equals(1)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.UNAUTHORIZED);
        }

        try {
            // 这里应该调用订单服务的取消方法
             orderService.cancel(orderId);
            log.info("管理员{}取消订单{}", userInfo.getId(), orderId);
            return "取消成功";
        } catch (Exception e) {
            log.error("管理员取消订单失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.SYSTEM_ERROR);
        }
    }

    /**
     * 应用排序逻辑
     * @param orders 订单列表
     * @param sortField 排序字段
     * @param sortOrder 排序方式
     * @return 排序后的订单列表
     */
    private List<Order> applySorting(List<Order> orders, String sortField, String sortOrder) {
        if (orders == null || orders.isEmpty()) {
            return orders;
        }

        // 默认排序字段和方式
        if (sortField == null || sortField.trim().isEmpty()) {
            sortField = "createTime";
        }
        if (sortOrder == null || sortOrder.trim().isEmpty()) {
            sortOrder = "desc";
        }

        try {
            Comparator<Order> comparator;

            // 根据排序字段选择比较器
            switch (sortField.toLowerCase()) {
                case "createtime":
                default:
                    // 按创建时间排序
                    comparator = Comparator.comparing(Order::getCreateTime);
                    break;
            }

            // 根据排序方式决定是否反转
            if ("asc".equalsIgnoreCase(sortOrder)) {
                // 正序：最早的在前面
                return orders.stream().sorted(comparator).collect(Collectors.toList());
            } else {
                // 倒序：最新的在前面（默认）
                return orders.stream().sorted(comparator.reversed()).collect(Collectors.toList());
            }

        } catch (Exception e) {
            log.warn("排序失败，使用默认排序: {}", e.getMessage());
            // 排序失败时，使用默认的创建时间倒序
            return orders.stream()
                    .sorted(Comparator.comparing(Order::getCreateTime).reversed())
                    .collect(Collectors.toList());
        }
    }
}