package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@TableName("weshop_goods")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Goods implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer categoryId;

    private String goodsSn;

    private String name;

    private Integer brandId;

    private Integer goodsNumber;

    private String keywords;

    private String goodsBrief;

    private Boolean isOnSale;

    private Date createTime;

    private Short sortOrder;

    private Boolean isDelete;

    private Integer attributeCategory;

    /**
     * 专柜价格
     */
    private BigDecimal counterPrice;

    /**
     * 附加价格
     */
    private BigDecimal extraPrice;

    private Boolean isNewly;

    /**
     * 商品单位
     */
    private String goodsUnit;

    /**
     * 商品主图
     */
    private String primaryPicUrl;

    /**
     * 商品列表图
     */
    private String listPicUrl;

    /**
     * 零售价格
     */
    private BigDecimal retailPrice;

    /**
     * 销售量
     */
    private Integer sellVolume;

    /**
     * 主sku　product_id
     */
    private Integer primaryProductId;

    /**
     * 单位价格，单价
     */
    private BigDecimal unitPrice;

    private String promotionDesc;

    private String promotionTag;

    /**
     * APP专享价
     */
    private BigDecimal appExclusivePrice;

    /**
     * 是否是APP专属
     */
    private Boolean isAppExclusive;

    private Boolean isLimited;

    private Boolean isHot;

    private String goodsDesc;

    /**
     * 商品展示系统，JSON格式存储多个系统ID
     */
    private String displaySystems;

    /**
     * 商品详情标签，多个标签用逗号分割
     */
    private String detailTag;

}
