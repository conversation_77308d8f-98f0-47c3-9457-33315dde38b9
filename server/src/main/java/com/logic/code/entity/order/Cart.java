package com.logic.code.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

@TableName( "weshop_cart")
public class Cart {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer userId;

    private String sessionId;

    private Integer goodsId;

    private String goodsSn;

    private Integer productId;

    private String goodsName;

    private BigDecimal marketPrice;

    private BigDecimal retailPrice;

    private Short number;

    /**
     * product表对应的goods_specification_ids
     */
    private String goodsSpecificationIds;

    private Boolean checked;

    private String listPicUrl;

    /**
     * 规格属性组成的字符串，用来显示用
     */
    private String goodsSpecificationNameValue;

    public Integer getId() {
        return id;
    }

    public Cart setId(Integer id) {
        this.id = id;
        return this;
    }

    public Integer getUserId() {
        return userId;
    }

    public Cart setUserId(Integer userId) {
        this.userId = userId;
        return this;
    }

    public String getSessionId() {
        return sessionId;
    }

    public Cart setSessionId(String sessionId) {
        this.sessionId = sessionId;
        return this;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public Cart setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
        return this;
    }

    public String getGoodsSn() {
        return goodsSn;
    }

    public Cart setGoodsSn(String goodsSn) {
        this.goodsSn = goodsSn;
        return this;
    }

    public Integer getProductId() {
        return productId;
    }

    public Cart setProductId(Integer productId) {
        this.productId = productId;
        return this;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public Cart setGoodsName(String goodsName) {
        this.goodsName = goodsName;
        return this;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public Cart setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
        return this;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public Cart setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
        return this;
    }

    public Short getNumber() {
        return number;
    }

    public Cart setNumber(Short number) {
        this.number = number;
        return this;
    }

    public String getGoodsSpecificationIds() {
        return goodsSpecificationIds;
    }

    public Cart setGoodsSpecificationIds(String goodsSpecificationIds) {
        this.goodsSpecificationIds = goodsSpecificationIds;
        return this;
    }

    public Boolean getChecked() {
        return checked;
    }

    public Cart setChecked(Boolean checked) {
        this.checked = checked;
        return this;
    }

    public String getListPicUrl() {
        return listPicUrl;
    }

    public Cart setListPicUrl(String listPicUrl) {
        this.listPicUrl = listPicUrl;
        return this;
    }

    public String getGoodsSpecificationNameValue() {
        return goodsSpecificationNameValue;
    }

    public Cart setGoodsSpecificationNameValue(String goodsSpecificationNameValue) {
        this.goodsSpecificationNameValue = goodsSpecificationNameValue;
        return this;
    }
}
