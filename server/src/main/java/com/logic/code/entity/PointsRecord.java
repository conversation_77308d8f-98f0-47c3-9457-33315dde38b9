package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 积分记录实体类
 */
@Data
@Accessors(chain = true)
@TableName("weshop_points_record")
public class PointsRecord {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 积分类型：earn(获得), use(使用)
     */
    private String type;
    
    /**
     * 积分数量（正数为获得，负数为使用）
     */
    private Integer points;
    
    /**
     * 积分来源：order(订单), manual(手动调整)
     */
    private String source;
    
    /**
     * 来源ID（如订单ID）
     */
    private Integer sourceId;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}