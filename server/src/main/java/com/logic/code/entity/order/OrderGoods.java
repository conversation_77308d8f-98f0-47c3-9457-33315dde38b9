package com.logic.code.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@TableName( "weshop_order_goods")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderGoods {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer orderId;

    private Integer goodsId;

    private String goodsName;

    private String goodsSn;

    private Integer productId;

    private Short number;

    private BigDecimal marketPrice;

    private BigDecimal retailPrice;

    private Boolean isReal;

    private String goodsSpecificationIds;

    private String listPicUrl;

    private String goodsSpecificationNameValue;


}
