package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户余额使用记录实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName("weshop_balance_record")
public class BalanceRecord {

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 操作类型：recharge-充值, use-使用, refund-退款, adjust-调整
     */
    private String type;

    /**
     * 金额（正数为增加，负数为减少）
     */
    private BigDecimal amount;

    /**
     * 操作前余额
     */
    private BigDecimal balanceBefore;

    /**
     * 操作后余额
     */
    private BigDecimal balanceAfter;

    /**
     * 来源：order-订单, recharge-充值, refund-退款, manual-手动调整
     */
    private String source;

    /**
     * 来源ID（如订单ID、充值记录ID等）
     */
    private Integer sourceId;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 操作员ID（管理员操作时记录）
     */
    private Long operatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // 常量定义
    public static final String TYPE_RECHARGE = "recharge";
    public static final String TYPE_USE = "use";
    public static final String TYPE_REFUND = "refund";
    public static final String TYPE_ADJUST = "adjust";

    public static final String SOURCE_ORDER = "order";
    public static final String SOURCE_RECHARGE = "recharge";
    public static final String SOURCE_REFUND = "refund";
    public static final String SOURCE_MANUAL = "manual";
    public static final String SOURCE_ORDER_CANCEL = "order_cancel";
}
