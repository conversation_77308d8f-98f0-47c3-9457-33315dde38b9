package com.logic.code.schedule;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.logic.code.common.utils.StringUtils;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderExpress;
import com.logic.code.service.OrderExpressService;
import com.logic.code.service.OrderExportService;
import com.logic.code.service.OrderService;
import com.logic.code.service.SalesVolumeUpdateService;
import com.logic.code.service.TokenService;
import jakarta.annotation.Resource;
import logic.orm.utils.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/31 20:20
 * @desc
 */

@Component
@Slf4j
public class TaskSchedule {


    @Resource
    OrderExpressService orderExpressService;

    @Resource
    OrderService orderService;

    @Resource
    OrderExportService orderExportService;

    @Resource
    SalesVolumeUpdateService salesVolumeUpdateService;

    @Resource
    TokenService tokenService;

    //@Scheduled(fixedRate = 4 * 60 * 60 * 1000)
    public void test() {

        List<OrderExpress> list = orderExpressService.queryAll();
        if (ListUtils.isNotBlank(list)) {
            for (OrderExpress express : list) {
                if (!"SIGN".equals(express.getLogisticsStatus())) {
                    Order order = orderService.queryById(express.getOrderId());
                    String mobile = order.getMobile();
                    String trace = getTrace(express.getLogisticCode(), mobile);
                    if (StringUtils.isBlank(trace)) continue;
                    List<Trace> traceList = new ArrayList<>();
                    JSONObject jsonObject = JSONObject.parseObject(trace);
                    if (jsonObject.getInteger("code") == 200) {
                        JSONObject data = jsonObject.getJSONObject("data");
                        String logisticsStatus = data.getString("logisticsStatus");
                        express.setLogisticsStatus(logisticsStatus);
                        JSONArray traceDetailList = data.getJSONArray("logisticsTraceDetailList");
                        for (int i = 0; i < traceDetailList.size(); i++) {
                            JSONObject routeDetail = traceDetailList.getJSONObject(i);
                            String desc = routeDetail.getString("desc");
                            String status = routeDetail.getString("logisticsStatus");
                            Long time = routeDetail.getLong("time");
                            traceList.add(new Trace(desc, DateUtil.formatDateTime(new Date(time)), status));
                        }
                        if (ListUtils.isNotBlank(traceList)) {
                            traceList = traceList.stream().sorted(Comparator.comparing(Trace::getDatetime).reversed()).collect(Collectors.toList());
                            express.setTraces(JSONObject.toJSONString(traceList));
                        }

                        orderExpressService.updateNotNull(express);
                    }
                }
            }
        }
    }


    public String getTrace(String no, String mobile) {
        String host = "https://kzexpress.market.alicloudapi.com";
        String path = "/api-mall/api/express/query";
        String appcode = "80d6cd58b7874015906b5d17bbe470a5";
        Map<String, String> headers = new HashMap();
        headers.put("Authorization", "APPCODE " + appcode);
        Map<String, Object> params = new HashMap<>();
        params.put("expressNo", no);
        params.put("mobile", mobile);

        try {
            HttpResponse response = HttpRequest.get(host + path).header("Authorization", "APPCODE " + appcode).form(params).execute();
            log.info(response.body());
            return response.body();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 定时随机增加商品销量
     * 每小时执行一次，随机为部分商品增加个位数的销量
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时 = 60 * 60 * 1000 毫秒
    public void scheduledSalesVolumeUpdate() {
        try {
            log.info("开始执行定时销量更新任务...");
            salesVolumeUpdateService.randomUpdateSalesVolume();
            log.info("定时销量更新任务执行完成");
        } catch (Exception e) {
            log.error("定时销量更新任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 定时更新已签收订单状态为已完成
     * 每天执行一次，将物流状态为SIGN且签收超过7天的订单状态更新为COMPLETED
     */
    @Scheduled(fixedRate = 24 * 60 * 60 * 1000) // 每天执行一次 = 24 * 60 * 60 * 1000 毫秒
    public void autoCompleteSignedOrders() {
        try {
            log.info("开始执行自动完成已签收订单任务...");

            // 获取所有物流信息
            List<OrderExpress> allExpressList = orderExpressService.queryAll();
            if (ListUtils.isBlank(allExpressList)) {
                log.info("没有找到任何物流信息");
                return;
            }

            // 计算7天前的时间
            Calendar sevenDaysAgo = Calendar.getInstance();
            sevenDaysAgo.add(Calendar.DAY_OF_MONTH, -7);
            Date sevenDaysAgoDate = sevenDaysAgo.getTime();

            int processedCount = 0;
            int successCount = 0;

            // 遍历所有物流信息，查找已签收且超过7天的订单
            for (OrderExpress express : allExpressList) {
                try {
                    // 检查物流状态是否为SIGN（已签收）
                    if (!"SIGN".equals(express.getLogisticsStatus())) {
                        continue;
                    }

                    // 检查更新时间是否超过7天
                    if (express.getUpdateTime() == null || express.getUpdateTime().after(sevenDaysAgoDate)) {
                        continue;
                    }

                    // 获取对应的订单
                    Order order = orderService.queryById(express.getOrderId());
                    if (order == null) {
                        log.warn("订单不存在，订单ID: {}", express.getOrderId());
                        continue;
                    }

                    // 检查订单状态是否已经是COMPLETED
                    if (order.getOrderStatus() == com.logic.code.common.enmus.OrderStatusEnum.COMPLETED) {
                        log.debug("订单{}状态已经是COMPLETED，跳过", order.getId());
                        continue;
                    }

                    processedCount++;

                    // 调用确认订单方法，将订单状态更新为COMPLETED
                    Boolean result = orderService.confirm(order.getId());
                    if (result) {
                        successCount++;
                        log.info("订单{}状态已自动更新为COMPLETED，物流签收时间: {}",
                                order.getId(), DateUtil.formatDateTime(express.getUpdateTime()));
                    } else {
                        log.warn("订单{}状态更新失败", order.getId());
                    }

                } catch (Exception e) {
                    log.error("处理订单{}时发生异常: {}", express.getOrderId(), e.getMessage(), e);
                }
            }

            log.info("自动完成已签收订单任务执行完成，处理订单数: {}, 成功更新数: {}", processedCount, successCount);

        } catch (Exception e) {
            log.error("自动完成已签收订单任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 定时清理token黑名单
     * 每小时执行一次，清理过期的黑名单条目
     */
   /* @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时 = 60 * 60 * 1000 毫秒
    public void cleanupTokenBlacklist() {
        try {
            log.debug("开始执行token黑名单清理任务...");
            tokenService.cleanupBlacklist();
            log.debug("token黑名单清理任务执行完成");
        } catch (Exception e) {
            log.error("token黑名单清理任务执行失败: {}", e.getMessage(), e);
        }
    }*/

}

@Data
@AllArgsConstructor
class Trace {
    private String content;
    private String datetime;
    private String status;
}
